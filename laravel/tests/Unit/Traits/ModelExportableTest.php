<?php

use PHPUnit\Framework\TestCase;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Traits\ModelExportable;
use Illuminate\Support\Facades\Excel;
use Mockery;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

/**
 * Test suite for ModelExportable trait
 *
 * Tests the refactored export functionality including:
 * - Export class name resolution
 * - Filename generation
 * - Error handling for missing export classes
 * - Backward compatibility with existing functionality
 */
class ModelExportableTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Mock all static facades and classes
        if (!class_exists('Mockery\Adapter\Phpunit\MockeryPHPUnitIntegration')) {
            $this->addToAssertionCount(1); // Prevent risky test warnings
        }
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test that resolveExportClassName works for App namespace models
     */
    public function testResolveExportClassNameForAppNamespace(): void
    {
        // Create a test model class
        $testModel = $this->createTestModel('App\Product');

        // Use reflection to test the private method
        $reflection = new \ReflectionClass($testModel);
        $method = $reflection->getMethod('resolveExportClassName');
        $method->setAccessible(true);

        // Mock class_exists to return true for the expected export class
        $this->mockClassExists('App\Exports\ProductsExport', true);

        $result = $method->invoke(null);

        $this->assertEquals('App\Exports\ProductsExport', $result);
    }

    /**
     * Test that resolveExportClassName works for App\Models namespace models
     */
    public function testResolveExportClassNameForModelsNamespace(): void
    {
        // Create a test model class
        $testModel = $this->createTestModel('App\Models\Coaching\Question');

        // Use reflection to test the private method
        $reflection = new \ReflectionClass($testModel);
        $method = $reflection->getMethod('resolveExportClassName');
        $method->setAccessible(true);

        // Mock class_exists to return true for the expected export class
        $this->mockClassExists('App\Exports\Coaching\QuestionsExport', true);

        $result = $method->invoke(null);

        $this->assertEquals('App\Exports\Coaching\QuestionsExport', $result);
    }

    /**
     * Test that resolveExportClassName throws exception when export class doesn't exist
     */
    public function testResolveExportClassNameThrowsExceptionWhenClassDoesNotExist(): void
    {
        $this->expectException(CrmException::class);
        $this->expectExceptionMessage("Export class 'App\Exports\NonExistentModelsExport' does not exist for model 'NonExistentModel'");

        // Create a test model class
        $testModel = $this->createTestModel('App\NonExistentModel');

        // Use reflection to test the private method
        $reflection = new \ReflectionClass($testModel);
        $method = $reflection->getMethod('resolveExportClassName');
        $method->setAccessible(true);

        // Mock class_exists to return false
        $this->mockClassExists('App\Exports\NonExistentModelsExport', false);

        $method->invoke(null);
    }

    /**
     * Test filename generation with different writer types
     */
    public function testGenerateExportFilename(): void
    {
        // Create a test model class
        $testModel = $this->createTestModel('App\Product');

        // Use reflection to test the private method
        $reflection = new \ReflectionClass($testModel);
        $method = $reflection->getMethod('generateExportFilename');
        $method->setAccessible(true);

        // Test different writer types
        $this->assertEquals('products.xlsx', $method->invoke(null, 'XLSX'));
        $this->assertEquals('products.csv', $method->invoke(null, 'CSV'));
        $this->assertEquals('products.xls', $method->invoke(null, 'XLS'));
        $this->assertEquals('products.pdf', $method->invoke(null, 'PDF'));
    }

    /**
     * Test that filename generation works correctly for complex model names
     */
    public function testGenerateExportFilenameWithComplexModelNames(): void
    {
        // Test with nested namespace model
        $testModel = $this->createTestModel('App\Models\Coaching\Question');

        $reflection = new \ReflectionClass($testModel);
        $method = $reflection->getMethod('generateExportFilename');
        $method->setAccessible(true);

        $this->assertEquals('questions.xlsx', $method->invoke(null, 'xlsx'));
    }

    /**
     * Test backward compatibility - ensure the refactored code produces same results as original
     */
    public function testBackwardCompatibilityForAppNamespace(): void
    {
        $testModel = $this->createTestModel('App\Product');

        $reflection = new \ReflectionClass($testModel);
        $resolveMethod = $reflection->getMethod('resolveExportClassName');
        $resolveMethod->setAccessible(true);

        $filenameMethod = $reflection->getMethod('generateExportFilename');
        $filenameMethod->setAccessible(true);

        // Mock class exists
        $this->mockClassExists('App\Exports\ProductsExport', true);

        // Test that we get the expected export class name (same as original logic)
        $exportClassName = $resolveMethod->invoke(null);
        $this->assertEquals('App\Exports\ProductsExport', $exportClassName);

        // Test that we get the expected filename (same as original logic)
        $filename = $filenameMethod->invoke(null, 'xlsx');
        $this->assertEquals('products.xlsx', $filename);
    }

    /**
     * Test backward compatibility for Models namespace
     */
    public function testBackwardCompatibilityForModelsNamespace(): void
    {
        $testModel = $this->createTestModel('App\Models\Coaching\Question');

        $reflection = new \ReflectionClass($testModel);
        $resolveMethod = $reflection->getMethod('resolveExportClassName');
        $resolveMethod->setAccessible(true);

        // Mock class exists
        $this->mockClassExists('App\Exports\Coaching\QuestionsExport', true);

        // Test that we get the expected export class name (same as original logic)
        $exportClassName = $resolveMethod->invoke(null);
        $this->assertEquals('App\Exports\Coaching\QuestionsExport', $exportClassName);
    }

    /**
     * Create a test model class that uses the ModelExportable trait
     */
    private function createTestModel(string $className): string
    {
        $safeClassName = "TestModel_" . str_replace(['\\', '.'], '_', $className) . '_' . uniqid();

        $classCode = "
        class {$safeClassName} {
            use " . ModelExportable::class . ";

            public static function class() {
                return '{$className}';
            }
        }";

        eval($classCode);

        return $safeClassName;
    }

    /**
     * Mock the class_exists function by creating dummy classes
     */
    private function mockClassExists(string $className, bool $exists): void
    {
        if ($exists && !class_exists($className)) {
            // Create a dummy class to simulate existence
            $safeClassName = str_replace(['\\', '.'], '_', $className);
            eval("class {$safeClassName} {}");

            // Create an alias if needed
            if (!class_exists($className)) {
                class_alias($safeClassName, $className);
            }
        }
    }
}

<?php

namespace Database\Seeders;

use App\DashboardSetting;
use App\Models\ApprovalSetting;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class ApprovalSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $actual_settings =
            [
                [
                    'name' => 'Vacation Approval Center Work With Flow',
                    'key' => 'vacation_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Commercial and Branding Approval Center Work With Flow',
                    'key' => 'commercial_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Expense Approval Center Work With Flow',
                    'key' => 'expense_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Plan Approval Center Work With Flow',
                    'key' => 'plan_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Actual Approval Center Work With Flow',
                    'key' => 'actual_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'OW Approval Center Work With Flow',
                    'key' => 'ow_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Active & Inactive Approval Center Work With Flow',
                    'key' => 'active_inactive_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Account Request Approval Center Work With Flow',
                    'key' => 'account_request_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Commercial Bill Approval Center Work With Flow',
                    'key' => 'commercial_bill_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Material Approval Center Work With Flow',
                    'key' => 'material_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'Change Plan Approval Center Work With Flow',
                    'key' => 'change_plan_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'name' => 'PV Approval Center Work With Flow',
                    'key' => 'pv_approval_center_flow',
                    'value' => 'No',
                    'type' => 'select',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
            ];

        Schema::disableForeignKeyConstraints();
        ApprovalSetting::truncate();
        Schema::enableForeignKeyConstraints();

        $chunked_actual_settings = array_chunk($actual_settings, 5);


        foreach ($chunked_actual_settings as $value) {
            ApprovalSetting::insert($value);
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddPerformanceIndexesForCustomerVisitsReport extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add indexes for sales tables
        Schema::table('sales', function (Blueprint $table) {
            $table->index(['date'], 'idx_sales_date_customer_visits_report');
            $table->index(['date', 'value'], 'idx_sales_date_value_customer_visits_report');
        });

        Schema::table('sales_details', function (Blueprint $table) {
            $table->index(['date'], 'idx_sales_details_date_customer_visits_report');
            $table->index(['div_id', 'brick_id', 'date'], 'idx_sales_details_div_brick_date_customer_visits_report');
            $table->index(['date', 'value'], 'idx_sales_details_date_value_customer_visits_report');
        });

        // Add indexes for mapping and linked pharmacies
        Schema::table('mapping_sale', function (Blueprint $table) {
            $table->index(['sale_id', 'mapping_id'], 'idx_mapping_sale_ids_customer_visits_report');
        });

        Schema::table('linked_pharmacies', function (Blueprint $table) {
            $table->index(['account_id', 'pharmable_type'], 'idx_linked_pharmacies_account_type_customer_visits_report');
            $table->index(['pharmable_id', 'pharmable_type'], 'idx_linked_pharmacies_polymorphic_customer_visits_report');
        });

        //Add indexes for account and doctor relationships
        Schema::table('account_lines', function (Blueprint $table) {
            $table->index(['account_id', 'line_id', 'line_division_id'], 'idx_account_lines_composite_customer_visits_report');
            $table->index(['from_date', 'to_date'], 'idx_account_lines_dates_customer_visits_report');
            $table->index(['deleted_at'], 'idx_account_lines_deleted_customer_visits_report');
        });

        Schema::table('new_account_doctors', function (Blueprint $table) {
            $table->index(['account_id', 'doctor_id', 'line_id'], 'idx_new_account_doctors_composite_customer_visits_report');
            $table->index(['from_date', 'to_date'], 'idx_new_account_doctors_dates_customer_visits_report');
            $table->index(['deleted_at'], 'idx_new_account_doctors_deleted_customer_visits_report');
        });

        // Add indexes for commercial requests
        Schema::table('commercial_requests', function (Blueprint $table) {
            $table->index(['created_at'], 'idx_commercial_requests_created_customer_visits_report');
            $table->index(['deleted_at'], 'idx_commercial_requests_deleted_customer_visits_report');
            $table->index(['request_type_id'], 'idx_commercial_requests_type_customer_visits_report');
            $table->index(['user_id'], 'idx_commercial_requests_user_customer_visits_report');
        });

        Schema::table('commercial_doctors', function (Blueprint $table) {
            $table->index(['doctor_id', 'request_id'], 'idx_commercial_doctors_composite_customer_visits_report');
            $table->index(['request_id'], 'idx_commercial_doctors_request_customer_visits_report');
        });

        Schema::table('commercial_lines', function (Blueprint $table) {
            $table->index(['request_id', 'line_id'], 'idx_commercial_lines_composite_customer_visits_report');
        });

        Schema::table('commercial_products', function (Blueprint $table) {
            $table->index(['request_id', 'product_id'], 'idx_commercial_products_composite_customer_visits_report');
            $table->index(['deleted_at'], 'idx_commercial_products_deleted_customer_visits_report');
        });

        Schema::table('commercial_out_of_lists', function (Blueprint $table) {
            $table->index(['request_id'], 'idx_commercial_out_of_lists_request_customer_visits_report');
        });

        // Add indexes for visits
        Schema::table('actual_visits', function (Blueprint $table) {
            $table->index(['account_dr_id', 'div_id'], 'idx_actual_visits_account_div_customer_visits_report');
            $table->index(['visit_date'], 'idx_actual_visits_date_customer_visits_report');
            $table->index(['line_id'], 'idx_actual_visits_line_customer_visits_report');
            $table->index(['visit_date', 'line_id'], 'idx_actual_visits_date_line_customer_visits_report');
        });

        Schema::table('plan_visit_details', function (Blueprint $table) {
            $table->index(['visitable_id', 'visitable_type'], 'idx_plan_visit_details_polymorphic_customer_visits_report');
            $table->index(['approval'], 'idx_plan_visit_details_approval_customer_visits_report');
        });

        // Add indexes for line products
        Schema::table('line_products', function (Blueprint $table) {
            $table->index(['line_id', 'product_id'], 'idx_line_products_composite_customer_visits_report');
            $table->index(['from_date', 'to_date'], 'idx_line_products_dates_customer_visits_report');
            $table->index(['deleted_at'], 'idx_line_products_deleted_customer_visits_report');
        });

        // Add indexes for doctors and specialities
        Schema::table('doctors', function (Blueprint $table) {
            $table->index(['speciality_id'], 'idx_doctors_speciality_customer_visits_report');
            $table->index(['class_id'], 'idx_doctors_class_customer_visits_report');
            $table->index(['deleted_at'], 'idx_doctors_deleted_customer_visits_report');
        });

        // Add indexes for accounts
        Schema::table('accounts', function (Blueprint $table) {
            $table->index(['type_id'], 'idx_accounts_type_customer_visits_report');
        });

        // Add indexes for account types
        Schema::table('account_types', function (Blueprint $table) {
            $table->index(['shift_id'], 'idx_account_types_shift_customer_visits_report');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Drop indexes in reverse order
        Schema::table('account_types', function (Blueprint $table) {
            $table->dropIndex('idx_account_types_shift_customer_visits_report');
        });

        Schema::table('accounts', function (Blueprint $table) {
            $table->dropIndex('idx_accounts_type_customer_visits_report');
        });

        Schema::table('doctors', function (Blueprint $table) {
            $table->dropIndex('idx_doctors_speciality_customer_visits_report');
            $table->dropIndex('idx_doctors_class_customer_visits_report');
            $table->dropIndex('idx_doctors_deleted_customer_visits_report');
        });

        Schema::table('line_products', function (Blueprint $table) {
            $table->dropIndex('idx_line_products_composite_customer_visits_report');
            $table->dropIndex('idx_line_products_dates_customer_visits_report');
            $table->dropIndex('idx_line_products_deleted_customer_visits_report');
        });

        Schema::table('plan_visit_details', function (Blueprint $table) {
            $table->dropIndex('idx_plan_visit_details_polymorphic_customer_visits_report');
            $table->dropIndex('idx_plan_visit_details_approval_customer_visits_report');
        });

        Schema::table('actual_visits', function (Blueprint $table) {
            $table->dropIndex('idx_actual_visits_account_div_customer_visits_report');
            $table->dropIndex('idx_actual_visits_date_customer_visits_report');
            $table->dropIndex('idx_actual_visits_line_customer_visits_report');
            $table->dropIndex('idx_actual_visits_date_line_customer_visits_report');
        });

        Schema::table('commercial_out_of_lists', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_out_of_lists_request_customer_visits_report');
        });

        Schema::table('commercial_products', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_products_composite_customer_visits_report');
            $table->dropIndex('idx_commercial_products_deleted_customer_visits_report');
        });

        Schema::table('commercial_lines', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_lines_composite_customer_visits_report');
        });

        Schema::table('commercial_doctors', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_doctors_composite_customer_visits_report');
            $table->dropIndex('idx_commercial_doctors_request_customer_visits_report');
        });

        Schema::table('commercial_requests', function (Blueprint $table) {
            $table->dropIndex('idx_commercial_requests_created_customer_visits_report');
            $table->dropIndex('idx_commercial_requests_deleted_customer_visits_report');
            $table->dropIndex('idx_commercial_requests_type_customer_visits_report');
            $table->dropIndex('idx_commercial_requests_user_customer_visits_report');
        });

        Schema::table('new_account_doctors', function (Blueprint $table) {
            $table->dropIndex('idx_new_account_doctors_composite_customer_visits_report');
            $table->dropIndex('idx_new_account_doctors_dates_customer_visits_report');
            $table->dropIndex('idx_new_account_doctors_deleted_customer_visits_report');
        });

        Schema::table('account_lines', function (Blueprint $table) {
            $table->dropIndex('idx_account_lines_composite_customer_visits_report');
            $table->dropIndex('idx_account_lines_dates_customer_visits_report');
            $table->dropIndex('idx_account_lines_deleted_customer_visits_report');
        });

        Schema::table('linked_pharmacies', function (Blueprint $table) {
            $table->dropIndex('idx_linked_pharmacies_account_type_customer_visits_report');
            $table->dropIndex('idx_linked_pharmacies_polymorphic_customer_visits_report');
        });

        Schema::table('mapping_sale', function (Blueprint $table) {
            $table->dropIndex('idx_mapping_sale_ids_customer_visits_report');
        });

        Schema::table('sales_details', function (Blueprint $table) {
            $table->dropIndex('idx_sales_details_date_customer_visits_report');
            $table->dropIndex('idx_sales_details_div_brick_date_customer_visits_report');
            $table->dropIndex('idx_sales_details_date_value_customer_visits_report');
        });

        Schema::table('sales', function (Blueprint $table) {
            $table->dropIndex('idx_sales_date_customer_visits_report');
            $table->dropIndex('idx_sales_date_value_customer_visits_report');
        });
    }
}

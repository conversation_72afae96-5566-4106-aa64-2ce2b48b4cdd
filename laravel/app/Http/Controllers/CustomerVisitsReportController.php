<?php

namespace App\Http\Controllers;

use App\Account;
use App\ActualVisit;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\Mapping;
use App\Models\CommercialRequest\CommercialDoctor;
use App\Models\CommercialRequest\CommercialOutOfList;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\ListSetting;
use App\PlanVisit;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CustomerVisitsReportController extends ApiController
{
    private function getSalesPerBrickSubQuery($from, $to)
    {
        return DB::table('sales_details')
            ->selectRaw('IFNULL(SUM(crm_sales_details.value), 0)') // select unique details
            ->whereColumn('sales_details.div_id', 'line_divisions.id') // IMPORTANT: dynamic per-brick match
            ->whereColumn('sales_details.brick_id', 'bricks.id') // IMPORTANT: dynamic per-brick match
            ->whereBetween('sales_details.date', [$from->toDateString(), $to->toDateString()]);
    }
    private function getSalesSubQuery($from, $to)
    {
        $distinctSales = DB::table('mapping_sale')
            ->join('sales', 'mapping_sale.sale_id', '=', 'sales.id')
            ->join('linked_pharmacies', function ($join) {
                $join->on('linked_pharmacies.pharmable_id', '=', 'mapping_sale.mapping_id')
                    ->where('linked_pharmacies.pharmable_type', Mapping::class);
            })
            ->whereColumn('linked_pharmacies.account_id', 'accounts.id')
            ->whereBetween('sales.date', [$from->toDateString(), $to->toDateString()])
            ->select('sales.id')
            ->distinct();

        return DB::table('sales_details')
            ->selectRaw('IFNULL(SUM(crm_sales_details.value), 0)')
            ->whereColumn('sales_details.div_id', 'line_divisions.id') // IMPORTANT: dynamic per-brick match
            ->whereColumn('sales_details.brick_id', 'bricks.id') // IMPORTANT: dynamic per-brick match
            ->whereBetween('sales_details.date', [$from->toDateString(), $to->toDateString()])
            ->whereIn('sales_details.sale_id', $distinctSales);
    }

    private function getDoctors(array $divisions, array $accountTypes, array $specialities, array $req, Carbon $from, Carbon $to, array $userLines, $approvals)
    {
        $setting = ListSetting::where('key', 'list_type')->value('value');
        $doctors = Account::select(
            [
                'doctors.id as id',
                'doctors.name as doctor',
                DB::raw('IFNULL(group_concat(distinct crm_accounts.name),"") as account'),
                DB::raw('IFNULL(group_concat(distinct crm_accounts.id),"") as account_id'),
                DB::raw('IFNULL(group_concat(distinct crm_accounts.code),"") as acc_code'),
                // DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
                DB::raw('IFNULL(group_concat(distinct crm_lines.id),"") as line_id'),
                'bricks.name as brick',
                'bricks.name as brick_id',
                'line_divisions.name as division',
                'line_divisions.id as div_id',
                // DB::raw('IFNULL(group_concat(distinct crm_line_divisions.name),"") as division'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.name),"") as account_type'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.shift_id),"") as acc_shift_id'),
                DB::raw('IFNULL(group_concat(distinct crm_account_types.id),"") as account_type_id'),
                'doctors.ucode as ucode',
                DB::raw('IFNULL(group_concat(distinct crm_d.name),"") as class'),
                DB::raw('IFNULL(group_concat(distinct crm_d.id),"") as doc_class_id'),
                'specialities.name as speciality',
                'specialities.id as speciality_id',
                // DB::raw('Count(distinct crm_planned_visits.id) as no_plans'),
                DB::raw('Count(distinct crm_actual_visits.id) as no_visits'),
                DB::raw('Count(distinct crm_b.id) as total_requests'),
                DB::raw('IFNULL(group_concat(distinct crm_b.id),"") as request_ids'),
                "sales" => $this->getSalesSubQuery($from, $to),
                "brick_sales" => $this->getSalesPerBrickSubQuery($from, $to),
            ]
        )
            ->join('account_types', function ($join) use ($accountTypes) {
                if (!empty($accountTypes)) {
                    $join->on('accounts.type_id', 'account_types.id')
                        // ->where('account_types.shift_id', '<>', 3)
                        ->whereIntegerInRaw('accounts.type_id', $accountTypes);
                }
            })
            ->join('account_lines', function ($join) use ($userLines, $divisions, $from, $to) {
                $join->on('accounts.id', 'account_lines.account_id')
                    ->whereNull('account_lines.deleted_at')
                    ->whereIntegerInRaw('account_lines.line_id', $userLines)
                    ->whereIntegerInRaw('account_lines.line_division_id', $divisions)
                    ->where(function ($query) use ($from, $to) {
                        $query->where(function ($subQuery) use ($from, $to) {
                            $subQuery->whereNull('account_lines.to_date') // Active records
                                ->orWhereBetween('account_lines.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                ->orWhere('account_lines.to_date', '>=', $to->toDateString()); // Ends within range
                        })
                            ->where(function ($subQuery) use ($from, $to) {
                                $subQuery->where('account_lines.from_date', '<=', $from->toDateString()) // Starts before range
                                    ->orWhereBetween('account_lines.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                            });
                    });
            });
        if ($setting != 'Default') {
            $doctors = $doctors
                ->join(
                    'new_account_doctors',
                    function ($join) use ($userLines, $from, $to) {
                        $join->on('accounts.id', '=', 'new_account_doctors.account_id');
                        $join->on('account_lines.id', '=', 'new_account_doctors.account_lines_id')
                            ->whereIntegerInRaw('new_account_doctors.line_id', $userLines)
                            ->where(function ($query) use ($from, $to) {
                                $query->where(function ($subQuery) use ($from, $to) {
                                    $subQuery->whereNull('new_account_doctors.to_date') // Active records
                                        ->orWhereBetween('new_account_doctors.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                        ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()); // Ends within range
                                })
                                    ->where(function ($subQuery) use ($from, $to) {
                                        $subQuery->where('new_account_doctors.from_date', '<=', $from->toDateString()) // Starts before range
                                            ->orWhereBetween('new_account_doctors.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                                    });
                            });
                    }
                );
        } else {
            $doctors = $doctors
                ->join('new_account_doctors', function ($join) use ($userLines, $from, $to) {
                    $join->on('accounts.id', 'new_account_doctors.account_id')
                        ->whereIntegerInRaw('new_account_doctors.line_id', $userLines)
                        ->where(function ($query) use ($from, $to) {
                            $query->where(function ($subQuery) use ($from, $to) {
                                $subQuery->whereNull('new_account_doctors.to_date') // Active records
                                    ->orWhereBetween('new_account_doctors.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                                    ->orWhere('new_account_doctors.to_date', '>=', $to->toDateString()); // Ends within range
                            })
                                ->where(function ($subQuery) use ($from, $to) {
                                    $subQuery->where('new_account_doctors.from_date', '<=', $from->toDateString()) // Starts before range
                                        ->orWhereBetween('new_account_doctors.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                                });
                        });
                });
        }
        $doctors = $doctors->join('doctors', function ($join) use ($specialities) {
            $join->on('new_account_doctors.doctor_id', '=', 'doctors.id')
                ->whereNull('doctors.deleted_at');
            if (!empty($specialities)) {
                $join->whereIntegerInRaw('doctors.speciality_id', $specialities);
            }
        })
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('classes as d', 'doctors.class_id', 'd.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('actual_visits', function ($join) use ($from, $to, $userLines) {
                $join->on('doctors.id', 'actual_visits.account_dr_id')
                    ->on('line_divisions.id', 'actual_visits.div_id')
                    ->wherebetween('actual_visits.visit_date', [$from, $to])
                    ->whereIntegerInRaw('actual_visits.line_id', $userLines);
            })
            // ->leftJoin('planned_visits', function ($join) use ($from, $to, $userLines) {
            //     $join->on('doctors.id', 'planned_visits.account_dr_id')
            //         ->wherebetween('planned_visits.visit_date', [$from, $to])
            //         ->whereIntegerInRaw('actual_visits.line_id', $userLines);
            // })
            ->leftJoin('commercial_doctors', function ($join) {
                $join->on('doctors.id', 'commercial_doctors.doctor_id');
            })
            ->leftJoin('commercial_requests as a', function ($join) use ($from, $to, $approvals) {
                $join->on('commercial_doctors.request_id', 'a.id')
                    ->whereBetween('a.created_at', [$from, $to]);
            })
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('a.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', CommercialRequest::class);
                }
            )
            ->leftJoin('commercial_requests as b', function ($join) use ($approvals) {
                $join->on('a.id', 'b.id');
                if ($approvals == 1) {
                    $join->whereNull('plan_visit_details.approval');
                }
                if ($approvals == 2) {
                    $join->where('plan_visit_details.approval', 1);
                }
                if ($approvals == 3) {
                    $join->where('plan_visit_details.approval', 0);
                }
                if ($approvals == 4) {
                    $join->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null));
                }
            })
            ->leftJoin('commercial_lines', function ($join) use ($req) {
                $join->on('a.id', 'commercial_lines.request_id')
                    ->whereIntegerInRaw('commercial_lines.line_id', $req['line']);
            })
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->whereNull('new_account_doctors.deleted_at')
            ->whereNull('a.deleted_at')
            ->groupBy("accounts.id", "doctors.id", 'line_divisions.id', "bricks.id");
        if (!empty($userLines)) {
            $doctors->whereIntegerInRaw('new_account_doctors.line_id', $userLines);
        }
        // throw new CrmException($doctors->get());
        return $doctors->get();
    }
    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $req_all = $request->visitFilter;
        $userLines = $authUser->userLines()->pluck('id')->toArray();
        $from = Carbon::parse($req_all['from'])->startOfMonth();
        $to =  Carbon::parse($req_all['to'])->endOfMonth();
        $filtered = collect([]);
        foreach ($req_all['line'] as $line) {
            $line = Line::find($line);
            $divisions = $line->divisions($from, $to)
                ->when(!empty($req_all['divisions']), fn($q) => $q->whereIntegerInRaw("line_divisions.id", $req_all['divisions']))->get();
            // throw new CrmException($divisions);
            $filtered = $filtered->merge($authUser->filterDivisions($line, $divisions, $req_all));
        }
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        $lastLevelDivisions = $filtered->where('division_type_id', $division_type)->where('is_kol', 0)->pluck('id')->unique()->toArray();
        $data = $this->getDoctors($lastLevelDivisions, $req_all['types'], $req_all['specialities'], $req_all, $from, $to, $userLines, $req_all['approval']);
        $doctors = $this->getDoctorData($data, $userLines, $from, $to, $req_all);
        $fields = collect([
            'id',
            'doctor',
            'ucode',
            'speciality',
            'class',
            'line',
            'division',
            'brick',
            'account',
            'account_id',
            // 'no_plans',
            'no_visits',
            // 'visit_date',
            'total_requests',
            'req_lines',
            'cost',
            'requests_per_line',
            'req_types',
            'cost_per_line',
            'sales',
            'roi',
            'brick_sales',
            'show'
        ]);
        // throw new CrmException($data);

        $clickable_fields = ['doctor', 'name', 'total_requests', 'no_plans', 'no_visits', 'requests_per_line'];
        return response()->json([
            'data' => $doctors,
            'fields' => $fields,
            'clickable_fields' => $clickable_fields
        ]);
    }

    public function getDoctorData($data, $userLines, $from, $to, $req_all)
    {
        $results = [];
        $cost = 0;
        foreach ($data as $doctor) {
            $requestData = $this->getRequests($userLines, $from, $to, $doctor);
            $cost = round($requestData->sum('cost'), 2);
            $requestLines = $requestData->pluck('line')->implode(" , ");
            // $requestTypes = $requestData->pluck('type')->unique()->implode(" , ");
            $requestDataPerLine = $this->getRequestsPerLine($from, $to, $doctor);
            // $costPerLine = round($requestDataPerLine->sum('cost'), 2);
            $requestPerLineCount = $requestDataPerLine['commercials']->count();
            $requestPerLineTypes = $requestDataPerLine['commercials']->pluck('type')->unique()->implode(" , ");
            $costPerLine = round($requestDataPerLine['costs'], 2);
            $roi = $cost > 0 ? $doctor->sales / $cost : 0;
            $results[] = [
                'id' => $doctor->id,
                'doctor' => $doctor->doctor,
                'ucode' => $doctor->ucode,
                'speciality' => $doctor->speciality,
                'class' => $doctor->class,
                'line' => $doctor->line,
                'line_id' => $doctor->line_id,
                'division' => $doctor->division,
                'div_id' => $doctor->div_id,
                'brick' => $doctor->brick,
                'brick_id' => $doctor->brick_id,
                'account' => $doctor->account,
                'account_id' => $doctor->account_id,
                // 'no_plans' => $doctor->no_plans,
                'no_visits' => $doctor->no_visits,
                'visit_date' => $doctor->visit_date,
                'total_requests' => $doctor->total_requests,
                'req_lines' => $requestLines,
                'cost' => $cost,
                'requests_per_line' => $requestPerLineCount,
                'req_types' => $requestPerLineTypes,
                // 'req_emp' => $requestUsers,
                'cost_per_line' => $costPerLine,
                'sales' => round($doctor->sales, 2),
                'roi' => round($roi, 2),
                'brick_sales' => round($doctor->brick_sales, 2),
            ];
        }
        return $results;
    }
    public function getRequests($lines, $from, $to, $doctor)
    {
        $requestIds = explode(",", $doctor?->request_ids);
        $commercials = DB::table('commercial_requests')->select(
            'commercial_requests.id as request_id',
            // 'request_types.name as type',
            // DB::raw('IFNULL(group_concat(distinct crm_request_types.name),"") as type'),
            DB::raw('IFNULL(group_concat(distinct crm_lines.name),"") as line'),
            DB::raw('IFNULL(group_concat(distinct crm_lines.id),"") as line_id'),
        )
            ->selectRaw('crm_commercial_requests.amount / (COUNT(distinct crm_commercial_doctors.id) + COUNT(distinct crm_commercial_out_of_lists.id)) as cost')
            ->leftJoin('commercial_lines', 'commercial_requests.id', 'commercial_lines.request_id')
            // ->leftJoin('request_types', 'commercial_requests.request_type_id', 'request_types.id')
            ->leftJoin(
                'plan_visit_details',
                function ($join) {
                    $join->on('commercial_requests.id', '=', 'plan_visit_details.visitable_id');
                    $join->where('plan_visit_details.visitable_type', CommercialRequest::class);
                }
            )
            ->leftJoin('commercial_divisions', 'commercial_requests.id', 'commercial_divisions.div_id')
            ->leftJoin('users', 'commercial_requests.user_id', 'users.id')
            ->leftJoin('lines', 'commercial_lines.line_id', 'lines.id')
            ->leftJoin('commercial_out_of_lists', 'commercial_requests.id', 'commercial_out_of_lists.request_id')
            ->leftJoin('commercial_doctors', 'commercial_requests.id', 'commercial_doctors.request_id')
            ->orderBy('commercial_requests.id', 'DESC')
            ->whereNull('commercial_requests.deleted_at')
            ->whereIntegerInRaw('commercial_requests.id', $requestIds)
            ->whereBetween('commercial_requests.created_at', [$from, $to]);
        if (!empty($lines)) {
            $commercials = $commercials->whereIntegerInRaw('lines.id', $lines);
        }
        $commercials = $commercials->groupBy("commercial_requests.id")->get();
        return $commercials;
    }
    public function getRequestsPerLine($from, $to, $doctor)
    {
        $requestPerLinsIds = explode(",", $doctor?->request_ids);
        $commercials = DB::table('commercial_requests')->select(
            'commercial_requests.id as request_id',
            DB::raw('Count(distinct crm_commercial_lines.request_id) as requests_per_line'),
            DB::raw('IFNULL(group_concat(distinct crm_request_types.name),"") as type'),
            DB::raw('IFNULL(group_concat(distinct crm_commercial_products.product_id),"") as product_id'),
            DB::raw("group_concat(distinct CONCAT(crm_commercial_products.ratio, '_', crm_commercial_products.product_id)) as ratio_mapping")
        )
            ->selectRaw('crm_commercial_requests.amount / (COUNT(distinct crm_commercial_doctors.id) + COUNT(distinct crm_commercial_out_of_lists.id)) as amount')
            ->whereIntegerInRaw('commercial_requests.id', $requestPerLinsIds)
            ->join('commercial_lines', function ($join) use ($doctor) {
                $join->on('commercial_requests.id', 'commercial_lines.request_id')
                    ->where('commercial_lines.line_id', $doctor->line_id);
            })
            ->leftJoin('request_types', 'commercial_requests.request_type_id', 'request_types.id')
            ->leftJoin('line_products', function ($join) use ($doctor) {
                $join->on('line_products.line_id', 'commercial_lines.line_id')
                    ->where('line_products.line_id', $doctor->line_id)
                    ->whereNull('line_products.deleted_at')
                    ->where('line_products.from_date', '<=', now())
                    ->where(fn($q) => $q->where('line_products.to_date', '>=', (string)Carbon::now())
                        ->orWhere('line_products.to_date', null));
            })
            ->leftjoin('commercial_products', function ($join) {
                $join->on('line_products.product_id', '=', 'commercial_products.product_id')
                    ->on('commercial_requests.id', '=', 'commercial_products.request_id')
                    ->whereNull('commercial_requests.deleted_at')
                    ->whereNull('commercial_products.deleted_at');
            })
            ->leftJoin('commercial_out_of_lists', 'commercial_requests.id', 'commercial_out_of_lists.request_id')
            ->leftJoin('commercial_doctors', 'commercial_requests.id', 'commercial_doctors.request_id')
            ->orderBy('commercial_requests.id', 'DESC')
            ->whereNotNull('commercial_products.product_id')
            ->whereBetween('commercial_requests.created_at', [$from, $to]);
        $commercials = $commercials->groupBy(
            "commercial_requests.id",
        )->get();
        $costs = 0;
        foreach ($commercials as $commercial) {
            $ratios = [];
            preg_match_all('/(\d+)_\d+/', $commercial->ratio_mapping, $ratios);
            if (count($ratios) == 2)
                $costs += array_sum($ratios[1]) * $commercial->amount / 100; // Sum all the ratios
        }

        return [
            'costs' => $costs,
            'commercials' => $commercials,
        ];
    }

    public function showData(Request $request)
    {
        // throw new CrmException($request->all());
        $column = $request->column;
        $visitFilter = $request->visitFilter;
        /**@var User */
        $user = Auth::user();
        $lines = $user->userLines()->pluck('id');
        $doctor_id = $request->item['id'];
        $line_id = $request->item['line_id'];
        $div_id = $request->item['div_id'];
        $from = Carbon::parse($visitFilter['from'])->startOfMonth();
        $to = Carbon::parse($visitFilter['to'])->endOfMonth();
        if ($column == 'total_requests') {
            return $this->respond(CommercialDoctor::whereHas(
                'commercial',
                function ($q) use ($from, $to) {
                    $q = $q->whereBetween('created_at', [$from, $to]);
                }
            )
                ->whereHas('commercial.details', function ($query) use ($visitFilter) {
                    if ($visitFilter['approval'] == 1) {
                        $query->whereNull('plan_visit_details.approval');
                    }
                    if ($visitFilter['approval'] == 2) {
                        $query->where('plan_visit_details.approval', 1);
                    }
                    if ($visitFilter['approval'] == 3) {
                        $query->where('plan_visit_details.approval', 0);
                    }
                    if ($visitFilter['approval'] == 4) {
                        $query->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null));
                    }
                })
                ->with('commercial')->where('doctor_id', $doctor_id)
                ->get()->map(function ($commercialDoctor) {
                    $commercialData  = $commercialDoctor->commercial;
                    $totalDoctors = $commercialData?->doctors()->count()
                        + CommercialOutOfList::where('request_id', $commercialData?->id)->count();
                    return [
                        'id' => $commercialData?->id,
                        'type' => $commercialData?->requestType?->name,
                        'user' => $commercialData?->user?->fullname,
                        'line' => $commercialData?->getCommercialLines?->pluck('name')->implode(' , '),
                        'total_doctors' => $totalDoctors,
                        'total' => $totalDoctors > 0 ? round($commercialData?->amount / $totalDoctors, 2) : 0,
                        'doctor_id' => $commercialDoctor->doctor?->id,
                        'doctor' => $commercialDoctor->doctor?->name,
                        'insertion' => Carbon::parse($commercialDoctor->commercial?->created_at)->toDateString(),
                        'tracing_actions' => ''
                    ];
                }));
        }
        if ($column == 'requests_per_line') {
            return $this->respond(CommercialDoctor::whereHas('commercial', fn($q) => $q->whereBetween('created_at', [$from, $to]))
                ->whereHas('commercial.details', function ($query) use ($visitFilter) {
                    if ($visitFilter['approval'] == 1) {
                        $query->whereNull('plan_visit_details.approval');
                    }
                    if ($visitFilter['approval'] == 2) {
                        $query->where('plan_visit_details.approval', 1);
                    }
                    if ($visitFilter['approval'] == 3) {
                        $query->where('plan_visit_details.approval', 0);
                    }
                    if ($visitFilter['approval'] == 4) {
                        $query->where(fn($q) => $q->where('plan_visit_details.approval', 1)->orWhere('plan_visit_details.approval', null));
                    }
                })
                ->with('commercial')->where('doctor_id', $doctor_id)
                ->whereHas('commercial.getCommercialLines', fn($q) => $q->where('lines.id', $line_id))->get()->map(function ($commercialDoctor) use ($line_id) {
                    $commercialData  = $commercialDoctor->commercial;
                    $totalDoctors = $commercialData?->doctors()->count()
                        + CommercialOutOfList::where('request_id', $commercialData?->id)->count();
                    $total = $totalDoctors > 0 ? round($commercialData?->amount / $totalDoctors, 2) : 0;
                    $ratios = $commercialData?->getCommercialProducts()->whereHas('lines', fn($q) => $q->where('lines.id', $line_id))->sum('ratio');
                    return [
                        'id' => $commercialData?->id,
                        'type' => $commercialData?->requestType?->name,
                        'user' => $commercialData?->user?->fullname,
                        'line' => $commercialData?->getCommercialLines?->pluck('name')->implode(' , '),
                        'total_doctors' => $totalDoctors,
                        'total' => round($ratios * $total / 100, 2),
                        'doctor_id' => $commercialDoctor->doctor?->id,
                        'doctor' => $commercialDoctor->doctor?->name,
                        'insertion' => Carbon::parse($commercialDoctor->commercial?->created_at)->toDateString(),
                        'tracing_actions' => ''
                    ];
                }));
        }
        if ($column == 'no_visits') {
            return $this->respond(ActualVisit::where('account_dr_id', $doctor_id)->where('div_id', $div_id)->whereBetween('visit_date', [$from, $to])
                ->whereIn('line_id', $lines)->get()->map(function ($actual) {
                    return [
                        'id' => $actual->id,
                        'date' => $actual->visit_date,
                        'user' => $actual->user->fullname,
                        'type' => $actual->visitType->name,
                        'line' => $actual->line->name,
                        'division' => $actual->division->name,
                        'account' => $actual->account?->name,
                        'account_id' => $actual->account?->id,
                        'doctor' => $actual->doctor != null ? $actual->doctor->name : "",
                        'doctor_id' => $actual->doctor != null ? $actual->doctor->id : "",
                        'speciality' => $actual->doctor != null ? $actual->doctor->speciality->name : "",
                        'acc_type' => $actual->account?->type != null ? $actual->account?->type?->name : "",
                        'shift' => $actual->shift != null ? $actual->shift?->name : "",
                    ];
                }));
        }
        if ($column == 'no_plans') {
            return $this->respond(PlanVisit::where('account_dr_id', $doctor_id)->whereBetween('visit_date', [$from, $to])->whereIn('line_id', $lines)->get()->map(function ($plan) {
                return [
                    'id' => $plan->id,
                    'date' => $plan->visit_date,
                    'user' => $plan->user->fullname,
                    'type' => $plan->visitType->name,
                    'line' => $plan->line->name,
                    'division' => $plan->division->name,
                    'account' => $plan->account?->name,
                    'account_id' => $plan->account?->id,
                    'doctor' => $plan->doctor != null ? $plan->doctor->name : "",
                    'doctor_id' => $plan->doctor != null ? $plan->doctor->id : "",
                    'speciality' => $plan->doctor != null ? $plan->doctor->speciality->name : "",
                    'acc_type' => $plan->account?->type != null ? $plan->account?->type?->name : "",
                    'shift' => $plan->shift != null ? $plan->shift?->name : "",
                ];
            }));
        }
    }
}

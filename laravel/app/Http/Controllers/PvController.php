<?php

namespace App\Http\Controllers;

use App\Exceptions\CrmException;
use App\Line;
use App\Models\PV\AgeGroup;
use App\Models\PV\AgeUnit;
use App\Models\PV\DoctorInformation;
use App\Models\PV\DrugInformation;
use App\Models\PV\EventAction;
use App\Models\PV\EventInformation;
use App\Models\PV\PatientInformation;
use App\Models\PV\PatientSex;
use App\Models\PV\PV;
use App\Models\PV\PvModule;
use App\Models\PV\Qualification;
use App\PlanVisitDetails;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PvController extends ApiController
{

    public function getData()
    {

        return $this->respond([
            'doctorQualifications' => Qualification::select('id', 'name')->get(),
            'pvModules' => PvModule::select('id', 'name')->get(),
            'ageGroups' => AgeGroup::select('id', 'name')->get(),
            'ageUnits' => AgeUnit::select('id', 'name')->get(),
            'actionDrugs' => EventAction::select('id', 'name')->get(),
            'sex' => PatientSex::select('id', 'name')->get(),
            'user_id' => Auth::id(),
        ]);
    }
    public function store(Request $request)
    {
        /**@var User  */
        $user = Auth::user();
        $line = Line::find($user->userLines()->first()?->id);
        $hasApprovable = $user->division($line)?->divisionType?->planable?->where('line_id', $line->id)->first()
            ->approvables()->wherePivot('request_type', PV::class)->count() > 0;
        $doctorInfo = $request->doctorInfo;
        $patientInfo = $request->patientInfo;
        $adverseEvent = $request->adverseEvent;
        $drugInfo = $request->drugInfo;
        DB::transaction(function () use ($user,$hasApprovable,$request, $doctorInfo, $patientInfo, $adverseEvent, $drugInfo) {
            $pv = PV::create($request->pv);
            $doctorInfo['pv_id'] = $pv->id;
            $patientInfo['pv_id'] = $pv->id;
            $adverseEvent['pv_id'] = $pv->id;
            foreach ($drugInfo as &$drug) {
                $drug['pv_id'] = $pv->id;
            }
            DoctorInformation::create($doctorInfo);
            PatientInformation::create($patientInfo);
            EventInformation::create($adverseEvent);
            DrugInformation::insert($drugInfo);
            PlanVisitDetails::firstOrCreate([
                'visitable_id' => $pv->id,
                'visitable_type' => PV::class,
            ], [
                'approval' => $hasApprovable ? null : 1,
                'created_by' => $user->id,
                'date' => $pv->submition_date,
            ]);
        });

        return $this->respondSuccess();
    }

    public function filter(Request $request)
    {
        $pv = $request->pvFilter;
        $users = User::whereIntegerInRaw('id', $pv['users'])->get();
        $from = Carbon::parse($pv['fromDate'])->startOfDay();
        $to = Carbon::parse($pv['toDate'])->endOfDay();
        $year = Carbon::parse($from)->format('Y');
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];

        $data = PV::select(
            'p_v_s.id as id',
            'p_v_s.visit_id as visit_id',
            'pv_modules.name as module',
            'users.fullname as employee',
            DB::raw('group_concat(distinct crm_pv_modules.name) as module'),
            DB::raw('DATE_FORMAT(crm_p_v_s.submition_date, "%Y-%m-%d") as date'),
            DB::raw('group_concat(distinct crm_doctors.name) as doctor'),
            DB::raw('group_concat(distinct crm_qualifications.name) as qualification'),
            DB::raw('group_concat(distinct crm_doctor_information.phone) as phone'),
            DB::raw('group_concat(distinct crm_doctor_information.email) as email'),
            DB::raw('group_concat(distinct crm_doctor_information.other_info) as other_info'),
            DB::raw('group_concat(distinct crm_doctor_information.comment) as doctor_comment'),
            DB::raw('group_concat(distinct crm_patient_information.name) as patient'),
            DB::raw('group_concat(distinct crm_age_units.name) as age_unit'),
            DB::raw('group_concat(distinct crm_patient_information.exact_age) as exact_age'),
            DB::raw('group_concat(distinct crm_age_groups.name) as age_group'),
            DB::raw('group_concat(distinct crm_patient_sex.name) as sex'),
            DB::raw('group_concat(distinct crm_patient_information.medical_history) as medical_history'),
            DB::raw('group_concat(distinct crm_patient_information.taking_medications) as taking_medications'),
            DB::raw('group_concat(distinct crm_patient_information.comment) as patient_comment'),
            DB::raw('group_concat(distinct crm_event_information.narrative) as event'),
            DB::raw('group_concat(distinct crm_event_information.reaction_start_date) as reaction_start_date'),
            DB::raw('group_concat(distinct crm_event_information.reaction_end_date) as reaction_end_date'),
            DB::raw('group_concat(distinct crm_event_actions.name) as action'),
            DB::raw('group_concat(distinct crm_event_information.comment) as event_comment'),
            'products.name as product',
            'drug_information.dosages as dosages',
            'drug_information.route_adminstration as route_adminstration',
            'drug_information.indication_for as indication_for',
            'drug_information.start_date as start_date',
            'drug_information.end_date as end_date',
            'drug_information.event_outcome as event_outcome',
            DB::raw('case when crm_drug_information.retake = 0 then "No" else "Yes" end as retake'),
            DB::raw('case when crm_drug_information.is_happened_again = 0 then "No" else "Yes" end as is_happened_again'),
            'drug_information.comment as drug_comment',
            // DB::raw('group_concat(distinct crm_drug_information.conc) as conc'),
            // DB::raw('group_concat(distinct crm_drug_information.formulation) as formulation'),
            // DB::raw('group_concat(distinct crm_drug_information.api) as api'),
            // DB::raw('group_concat(distinct crm_drug_information.dosages) as dosages'),
            // DB::raw('group_concat(distinct crm_drug_information.route_adminstration) as route_adminstration'),
            // DB::raw('group_concat(distinct crm_drug_information.indication_for) as indication_for'),
            // DB::raw('group_concat(distinct crm_drug_information.start_date) as start_date'),
            // DB::raw('group_concat(distinct crm_drug_information.end_date) as end_date'),
            // DB::raw('group_concat(distinct crm_drug_information.retake) as retake'),
            // DB::raw('group_concat(distinct crm_drug_information.is_happened_again) as is_happened_again'),
            // DB::raw('group_concat(distinct crm_drug_information.comment) as drug_comment'),
        )
            ->leftJoin('users', 'p_v_s.user_id', 'users.id')
            ->leftJoin('actual_visits', 'p_v_s.visit_id', 'actual_visits.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('doctor_information', 'p_v_s.id', 'doctor_information.pv_id')
            ->leftJoin('qualifications', 'doctor_information.qualification_id', 'qualifications.id')
            ->leftJoin('drug_information', 'p_v_s.id', 'drug_information.pv_id')
            ->leftJoin('products', 'drug_information.product_id', 'products.id')
            ->leftJoin('patient_information', 'p_v_s.id', 'patient_information.pv_id')
            ->leftJoin('age_units', 'patient_information.age_unit_id', 'age_units.id')
            ->leftJoin('age_groups', 'patient_information.age_group_id', 'age_groups.id')
            ->leftJoin('patient_sex', 'patient_information.sex_id', 'patient_sex.id')
            ->leftJoin('event_information', 'p_v_s.id', 'event_information.pv_id')
            ->leftJoin('event_actions', 'event_information.action_id', 'event_actions.id')
            ->leftJoin('pv_modules', 'p_v_s.pv_module_id', 'pv_modules.id')
            ->whereBetween('submition_date', [$from, $to])
            ->whereIn('p_v_s.user_id', $users->pluck('id'))
            ->groupBy('p_v_s.id', 'users.id', 'products.name', 'drug_information.id')
            ->get();

        $fields = [
            'id',
            'visit_id',
            'date',
            'module',
            'employee',
            'doctor',
            'qualification',
            'phone',
            'email',
            'other_info',
            'doctor_comment',
            'patient',
            'age_unit',
            'exact_age',
            'age_group',
            'sex',
            'medical_history',
            'taking_medications',
            'patient_comment',
            'event',
            'reaction_start_date',
            'reaction_end_date',
            'action',
            'event_comment',
            'product',
            'dosages',
            'route_adminstration',
            'indication_for',
            'start_date',
            'end_date',
            'event_outcome',
            'retake',
            'is_happened_again',
            'drug_comment',
        ];
        return response([
            'data' => $data,
            'dates' => $dates,
            'fields' => $fields,
        ]);
    }
}

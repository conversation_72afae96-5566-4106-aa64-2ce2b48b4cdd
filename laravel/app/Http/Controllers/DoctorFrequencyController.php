<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountLines;
use App\DivisionType;
use App\DoctorFrequency;
use App\Exceptions\CrmException;
use App\Helpers\ExcelImporter;
use App\Helpers\LogActivity;
use App\Http\Requests\DoctorFrequencyRequest;
use App\Http\Requests\ImportRequest;
use App\Http\Requests\MailRequest;
use App\Line;
use Maatwebsite\Excel\Excel as ExcelType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Imports\Updates\DoctorFrequenciesImport as UpdatesDoctorFrequenciesImport;
use App\LineUser;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;

class DoctorFrequencyController extends ApiController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $query = request('query');

        // Use query builder with index hints and optimize the query
        $doctor_frequencies = DB::table('doctor_frequencies')
            ->select(
                'doctor_frequencies.id',
                'lines.name as line',
                'accounts.name as account',
                'doctors.name as doctor',
                'doctor_frequencies.frequency',
                'doctor_frequencies.date'
            )
            ->leftJoin('lines', 'doctor_frequencies.line_id', '=', 'lines.id')
            ->leftJoin('accounts', 'doctor_frequencies.account_id', '=', 'accounts.id')
            ->leftJoin('doctors', 'doctor_frequencies.doctor_id', '=', 'doctors.id')
            ->whereNull('doctor_frequencies.deleted_at');

        // Only apply the search filter if a query is provided
        if ($query) {
            $doctor_frequencies->where(function ($q) use ($query) {
                $q->where('doctor_frequencies.id', 'Like', '%' . $query . '%')
                    ->orWhere('lines.name', 'Like', '%' . $query . '%')
                    ->orWhere('accounts.name', 'Like', '%' . $query . '%')
                    ->orWhere('doctors.name', 'Like', '%' . $query . '%')
                    ->orWhere('doctor_frequencies.frequency', 'Like', '%' . $query . '%')
                    ->orWhere('doctor_frequencies.date', 'Like', '%' . $query . '%');
            });
        }

        $doctor_frequencies = $doctor_frequencies->orderBy('date', 'desc')
            ->simplePaginate(300);

        LogActivity::addLog();
        return $this->respond(['doctor_frequencies' => $doctor_frequencies]);
    }

    public function date()
    {
        $date = Carbon::now()->startOfMonth()->toDateString();
        $min_date = Carbon::parse()->toDateString();
        return response()->json(compact('date', 'min_date'));
    }

    public function getList(Request $request)
    {
        $list = $request->listFilter;
        $accounts = Account::select(
            'accounts.id',
            'lines.name as line',
            'lines.id as line_id',
            'line_divisions.name as division',
            'bricks.name as brick',
            'accounts.name as account',
            'account_types.name as account_type',
            'accounts.address as address',
            'accounts.tel',
            'accounts.email',
            'doctors.name as doctor',
            'doctors.id as doctor_id',
            'doctors.ucode as ucode',
            'specialities.name as speciality',
            'accounts.active_date',
            'division_types.color'
        )
            ->leftJoin('account_types', 'accounts.type_id', 'account_types.id')
            ->leftJoin('account_lines', 'accounts.id', 'account_lines.account_id')
            ->leftJoin('new_account_doctors', 'accounts.id', 'new_account_doctors.account_id')
            ->leftJoin('lines', 'account_lines.line_id', 'lines.id')
            ->leftJoin('line_divisions', 'account_lines.line_division_id', 'line_divisions.id')
            ->leftJoin('bricks', 'account_lines.brick_id', 'bricks.id')
            ->leftJoin('line_users_divisions', 'account_lines.line_division_id', 'line_users_divisions.line_division_id')
            ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
            ->leftJoin('doctors', 'new_account_doctors.doctor_id', 'doctors.id')
            // ->leftJoin('doctor_frequencies', 'doctors.id', 'doctor_frequencies.doctor_id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->where(fn($q) => $q->where('accounts.inactive_date', '=', null)->orWhere('accounts.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('doctors.inactive_date', '=', null)->orWhere('doctors.inactive_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('account_lines.to_date', '=', null)->orWhere('account_lines.to_date', '>=', (string)Carbon::now()))
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()))
            ->where('accounts.active_date', '<=', Carbon::now())
            ->where('doctors.active_date', '<=', Carbon::now())
            ->where('new_account_doctors.from_date', '<=', Carbon::now())
            ->where('new_account_doctors.line_id', $list['line'])
            ->where('account_lines.line_id', $list['line'])
            // ->whereNotIn('doctors.id', $doctor_frequencies)
            ->whereIn('line_divisions.id', $list['divisions'])
            ->whereIn('accounts.type_id', $list['types'])
            ->whereIn('speciality_id', $list['specialities'])
            ->where('accounts.deleted_at', null)
            ->where('account_lines.deleted_at', null)
            ->where('new_account_doctors.deleted_at', null)
            ->where('doctors.deleted_at', null)
            ->get()->unique('doctor_id')->values();

        // Get all frequencies for the current month and line in a single query
        $month = Carbon::parse($list['date'])->format('m');
        $year = Carbon::parse($list['date'])->format('Y');
        $frequencies = DoctorFrequency::where('line_id', $list['line'])
            ->whereMonth('date', $month)
            ->whereYear('date', $year)
            ->get()
            ->keyBy(function ($item) {
                return $item->account_id . '-' . $item->doctor_id;
            });

        $accounts = $accounts->map(function ($account) use ($frequencies) {
            $key = $account->id . '-' . $account->doctor_id;
            $frequency = $frequencies->get($key);

            return [
                'id' => $account->id,
                'line' => $account->line,
                'line_id' => $account->line_id,
                'division' => $account->division,
                'brick' => $account->brick,
                'account' => $account->account,
                'type' => $account->account_type,
                'doctor' => $account->doctor,
                'doctor_id' => $account->doctor_id,
                'ucode' => $account->ucode,
                'spech' => $account->speciality,
                'frequency' => '',
                'frequency_id' => $frequency ? $frequency->id : null,
                'color' => $account->color,
            ];
        });

        $oldFrequencies = $frequencies->values();
        return $this->respond(compact('accounts', 'oldFrequencies'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function create()
    {
        $lines = Line::orderBy('sort', 'ASC')->select('lines.id', 'lines.name')->get();
        return response()->json(compact('lines'));
    }

    public function getLineAccounts(Line $line)
    {
        return response()->json(['line_accounts_names' => $line->accounts]);
    }

    public function getAccountDoctor(Account $account)
    {
        return response()->json(['account_doctors_names' => $account->activeAccountDoctors()->get()]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(DoctorFrequencyRequest $request)
    {
        $frequencies = $request->list;
        $month = Carbon::parse($frequencies[0]['date'])->format('m');
        $year = Carbon::parse($frequencies[0]['date'])->format('Y');
        foreach ($frequencies as $frequency) {
            DoctorFrequency::where('doctor_id', $frequency['doctor_id'])
                ->where('account_id', $frequency['account_id'])->where('line_id', $frequency['line_id'])
                ->whereMonth('date', $month)
                ->whereYear('date', $year)
                ->forceDelete();
            if ($frequency['frequency'] != null)
                DoctorFrequency::create($frequency);
        }
        LogActivity::addLog();
        return $this->respondSuccess();
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\DoctorFrequency  $DoctorFrequency
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $doctor_frequency = DB::table('doctor_frequencies')
            ->select(
                'doctor_frequencies.id',
                'lines.name as line',
                'accounts.name as account',
                'doctors.name as doctor',
                'doctor_frequencies.frequency',
                'doctor_frequencies.date'
            )
            ->leftJoin('lines', 'doctor_frequencies.line_id', '=', 'lines.id')
            ->leftJoin('accounts', 'doctor_frequencies.account_id', '=', 'accounts.id')
            ->leftJoin('doctors', 'doctor_frequencies.doctor_id', '=', 'doctors.id')
            ->where('doctor_frequencies.id', '=', $id)
            ->first();
        $model_id = $id;
        $model_type = DoctorFrequency::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json($doctor_frequency);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\DoctorFrequency  $DoctorFrequency
     * @return \Illuminate\Http\JsonResponse
     */
    public function edit($id)
    {
        $model_id = $id;
        $model_type = DoctorFrequency::class;

        // Get the specific doctor frequency
        $doctorFrequency = DoctorFrequency::find($id);

        if (!$doctorFrequency) {
            return response()->json(['error' => 'Doctor frequency not found'], 404);
        }

        // Only load the specific line and its related data
        $line = Line::with(['accounts' => function ($query) use ($doctorFrequency) {
            $query->where('id', $doctorFrequency->account_id)
                ->with(['activeAccountDoctors' => function ($query) use ($doctorFrequency) {
                    $query->where('doctor_id', $doctorFrequency->doctor_id);
                }]);
        }])->find($doctorFrequency->line_id);

        // Get all lines without eager loading for dropdown
        $lines = Line::select('id', 'name')->get();

        LogActivity::addLog($model_id, $model_type);
        return response()->json([
            'doctor_frequency' => $doctorFrequency,
            'line' => $line,
            'lines' => $lines,
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\DoctorFrequency  $DoctorFrequency
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        DoctorFrequency::find($id)->update($request->all());
        $model_id = $id;
        $model_type = DoctorFrequency::class;
        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\DoctorFrequency  $DoctorFrequency
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $doctor_frequency = DoctorFrequency::find($id);
        if ($doctor_frequency) {
            $doctor_frequency->delete();
        }
        $model_id = $id;
        $model_type = DoctorFrequency::class;

        LogActivity::addLog($model_id, $model_type);
        return response()->json(['status' => 'success']);
    }
    public function import(ImportRequest $request)
    {
        DoctorFrequency::import($request);
        return $this->respondSuccess();
    }

    public function updateByImport(ImportRequest $request)
    {
        DoctorFrequency::import($request, true);
        return $this->respondSuccess();
    }

    public function exportxlsx()
    {
        return DoctorFrequency::export(ExcelType::XLSX);
    }

    public function exportcsv()
    {
        return DoctorFrequency::export(ExcelType::CSV);
    }

    public function exportpdf()
    {
        // Use a more efficient query and limit to a reasonable number of records
        // or implement chunking for very large datasets
        $doctorfrequencies = DoctorFrequency::whereNull('deleted_at')
            ->orderBy('date', 'desc')
            ->limit(5000) // Limit to a reasonable number for PDF export
            ->get();

        return DoctorFrequency::exportPdf($doctorfrequencies);
    }

    public function sendmail(MailRequest $request)
    {
        // Use a more efficient query and limit to a reasonable number of records
        // or implement chunking for very large datasets
        $doctorfrequencies = DoctorFrequency::whereNull('deleted_at')
            ->orderBy('date', 'desc')
            ->limit(5000) // Limit to a reasonable number for email
            ->get();

        return DoctorFrequency::sendmail($request, $doctorfrequencies);
    }

    public function replicate()
    {
        $now = Carbon::now()->startOfMonth()->toDateString();
        $monthBefore = Carbon::now()->subMonth(1)->startOfMonth()->toDateString();
        DB::transaction(function () use ($now, $monthBefore) {
            DoctorFrequency::whereDate('date', $now)->forceDelete();
            $beforMonthFrequencies = DoctorFrequency::whereDate('date', $monthBefore)->get();

            $data = [];
            foreach ($beforMonthFrequencies as $beforMonthFrequency) {
                $data[] = [
                    'account_id' => $beforMonthFrequency->account_id,
                    'doctor_id' => $beforMonthFrequency->doctor_id,
                    'line_id' => $beforMonthFrequency->line_id,
                    'user_id' => $beforMonthFrequency->user_id,
                    'date' => $now,
                    'frequency' => $beforMonthFrequency->frequency,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            // Insert in chunks of 1000 (adjust if needed)
            $chunkSize = 1000;
            foreach (array_chunk($data, $chunkSize) as $chunk) {
                DoctorFrequency::insert($chunk);
            }
        });
        return $this->respondSuccess();
    }
}

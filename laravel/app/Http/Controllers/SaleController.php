<?php

namespace App\Http\Controllers;

use App\Action;
use App\Distributor;
use App\Exceptions\CrmException;
use App\Form;
use App\Helpers\LogActivity;
use App\Http\Repositories\Sales\SaleRepository;
use App\Http\Requests\SalesReaderRequest;
use App\Http\Requests\SalesRequest;
use App\Mapping;
use App\MappingDetail;
use App\Models\MappingSale;
use App\Permission;
use App\Product;
use App\Sale;
use App\SaleDetail;
use App\Services\Enums\Ceiling;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Response;

class SaleController extends ApiController
{

    public function __construct(private SaleRepository $repository)
    {
    }

    public function getDistributionDetails($ids)
    {
        $arr_ids = explode(",", $ids);
        $sales = Sale::whereIn("sale_ids",$arr_ids)
            ->orWhereIn("id", $arr_ids)
            ->with(["product","mappings","distributor"])
            ->get()
            ->transform(function ($sale) {
                $ceiling = Ceiling::from($sale->ceiling);
                $sale->ceiling = $ceiling->getName();
                return $sale;
            });

        return response()->json($sales);
    }

    public function index()
    {
        // throw new CrmException('hello');
        LogActivity::addLog();
        return $this->respond(
            Sale::leftJoin('products', 'sales.product_id', '=', 'products.id')
                ->where(
                    fn($q) => $q->where('sales.id', 'Like', '%' . request('query') . '%')
                        ->orWhere('products.name', 'Like', '%' . request('query') . '%')
                        ->orWhere('sales.value', 'Like', '%' . request('query') . '%')
                        ->orWhere('sales.date', 'Like', '%' . request('query') . '%')
                    // ->orWhere('lines.name', 'Like', '%' . request('query') . '%')
                )->get()->map(function ($sale) {
                    return [
                        'id' => $sale->id,
                        'brick' => $sale->mappings?->unique('mapping_id')->pluck('name')->implode(' , '),
                        'bonus' => $sale->bonus,
                        'sales' => $sale->quantity,
                        'products' => $sale->product->name,
                        'value' => $sale->value,
                        'date' => $sale->date,
                        'actions' => ''
                    ];
                })
        );


        // throw new CrmException($sales);
    }

    public function show(Sale $sale, $id)
    {
        $sale = Sale::find($id);
        // throw new CrmException($sale);
        // $model_id = $sale;
        // $model_type = Sale::class;
        // LogActivity::addLog( $model_id, $model_type);
        return $this->respond($sale);
    }

    public function store(SalesRequest $request)
    {
        // throw new CrmException($request->all);
        $mappingName = Mapping::find($request->mapping_id)->name;
        $product_line = Product::find($request->product_id)->lines()->get()->pluck('id');
        $mapping = Mapping::where('distributor_id', $request->distributor_id)
            ->where('mapping_type_id', $request->mapping_type_id)
            ->where('name', $mappingName)
            ->whereIn('line_id', $product_line)
            ->get()->pluck('id');
        $mapping_details = MappingDetail::whereIn('mapping_id', $mapping)->get();
        $percent_count = $mapping_details->sum('percent');
        if ($percent_count == 100) {
            DB::transaction(function () use ($request, $mapping_details) {
                $sale = Sale::create($request->validated());
                $sale->mappings()->sync($request->mapping_id);

                for ($i = 0; $i < $mapping_details->count(); $i++) {
                    $sale_details = new SaleDetail();
                    $sale_details->sale_id = $sale->id;
                    $sale_details->date = $request->date;
                    $sale_details->value = $request->value;
                    $sale_details->bonus = $request->bonus;
                    $sale_details->quantity = $request->quantity * $mapping_details[$i]['percent'] / 100;
                    $sale_details->div_id = $mapping_details[$i]['div_id'];
                    $sale_details->brick_id = $mapping_details[$i]['brick_id'];
                    $sale_details->save();
                }

                $model_id = $sale->id;
                $model_type = Sale::class;

                LogActivity::addLog($model_id, $model_type);
                return $this->respondCreated();
            });
        } else {
            return $this->respondWithErrorMessage("sum of percentage does not equal 100% .", 422);
        }
    }

    public function update(SalesRequest $request, Sale $sale, $id)
    {

        $sale = Sale::find($id);
        $sale_details = SaleDetail::where('sale_id', $sale->id)->get();
        $mapping = $sale->mappings()->get()->pluck('id');
        $mapping_details = MappingDetail::whereIn('mapping_id', $mapping)->get();
        DB::transaction(function () use ($request, $mapping_details, $sale, $sale_details) {
            $sale->bonus = $request->bonus;
            $sale->date = $request->date;
            $sale->quantity = $request->quantity;
            $sale->value = $request->value;
            $sale->save();

            for ($i = 0; $i < $mapping_details->count(); $i++) {
                foreach ($sale_details as $sale_detail) {
                    if ($sale_detail->div_id == $mapping_details[$i]['div_id'] && $sale_detail->brick_id = $mapping_details[$i]['brick_id'])
                        $sale_detail->bonus = $request->bonus;
                    $sale_detail->date = $request->date;
                    $sale_detail->value = $request->value;
                    $sale_detail->quantity = ($request->quantity * $mapping_details[$i]['percent']) / 100.00;
                    $sale_detail->save();
                }
            }

            $model_id = $sale->id;
            $model_type = Sale::class;
            LogActivity::addLog($model_id, $model_type);
            return $this->respond();
        });
    }

    public function destroy(Sale $sale)
    {
        $sale->delete();
        $model_id = $sale->id;
        $model_type = Sale::class;
        LogActivity::addLog($model_id, $model_type);
        return $this->respond();
    }
    public function getSalesData()
    {
        $products = Product::select('id', 'name')->get();
        $distributors = Distributor::select('id', 'name')->get();
        return $this->respond(['products' => $products, 'distributors' => $distributors]);
    }
    public function getSalesDetailsToDelete(Request $request)
    {
        $from = Carbon::parse($request['fromDate'])->startOfMonth()->toDateString();
        $to = Carbon::parse($request['toDate'])->endOfMonth()->toDateString();

        $sales = Sale::whereBetween('date', [$from, $to]);
        if (!empty($request['distributors'])) {
            $sales = $sales->whereIntegerInRaw('distributor_id', $request['distributors']);
        }
        if (!empty($request['products'])) {
            $sales = $sales->whereIntegerInRaw('product_id', $request['products']);
        }
        $sales = $sales->get()->pluck('id');
        SaleDetail::whereIntegerInRaw('sale_id', $sales)->forceDelete();
        MappingSale::whereIntegerInRaw('sale_id', $sales)->forceDelete();
        Sale::whereIntegerInRaw('id', $sales)->forceDelete();

        return $this->respondSuccess();
    }

    public function exportTemplate($filename)
    {
        $attributes = $this->repository->getFileAttributes($filename);
        return Response::download($attributes['path'], $filename, $attributes['headers']);
    }

    public function readFromFile(SalesReaderRequest $request)
    {
        $validData = $this->repository->readFile($request);
        return $this->respond($validData);
    }

    public function saveFromFile(SalesReaderRequest $request)
    {
        $this->repository->saveFile($request);
        return $this->respond();
    }
}

<?php

namespace App\Http\Controllers;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\LineProduct;
use App\TargetDetails;
use App\User;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rules\Unique;

class TargetDetailsReportController extends ApiController
{
    public function filter(Request $request)
    {
        /**@var User $authUser */
        $authUser = Auth::user();
        $target = $request->targetData;

        $perDivOrUserFilter = $target['filter'];
        $fields = $this->getFields($perDivOrUserFilter);

        $isDiv = $perDivOrUserFilter === 1;
        $isUser = $perDivOrUserFilter === 2;

        $from = Carbon::parse($target['fromDate'])->startOfDay()->toDateString();


        $to = Carbon::parse($target['toDate'])->endOfDay()->toDateString();
        $fromYear = Carbon::parse($from)->format('Y');
        $period = CarbonPeriod::create($from, "1 month", $to);


        $data = new Collection([]);
        $filtered = collect();

        $division_type = DivisionType::where('last_level', '=', 1)->value('id');

        foreach ($target['lines'] as $line_id) {
            $line = Line::find($line_id);

            $products = $line->products($from, $to)->when(
                !empty($target['products']),
                fn($q) => $q->whereIntegerInRaw("line_products.product_id", $target['products'])
            )->pluck('products.id');
            if ($request->product) {
                $products = $line->products($from, $to)->whereIntegerInRaw("line_products.product_id", [$request->product])->pluck('products.id');
            }
            $divisions = [];


            if ($isDiv) {
                $divisions = $line->divisions($from, $to)
                    ->when(!empty($target['divisions']), fn($q) => $q->whereIn("line_divisions.id", $target['divisions']))->get();

                $filteredDivs = $filtered->merge($authUser->filterDivisions($line, $divisions, $target, $from, $to))
                    ->where('division_type_id', '=', $division_type)->unique('id')->pluck('id')->toArray();
                foreach ($period as $date) {
                    $fromDate = Carbon::parse($date)->format("Y-m-d");
                    $dateToBeEnd = $date->endOfMonth();
                    $toDate = $dateToBeEnd->gt($to) ? $to : $date->endOfMonth();

                    $data = $data->merge(
                        $this->detailsPerDivision(
                            $filteredDivs,
                            $products,
                            $fromDate,
                            Carbon::parse($toDate)->toDateString(),
                            $period,
                            $fromYear
                        )
                    );
                }
            }

            if ($isUser) {
                $users = $line->users($from, $to)
                    ->when(!empty($target['users']), fn($q) => $q->whereIn("line_users.user_id", $target['users']))->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $target, $from, $to));
                $data = $data->merge($this->detailsPerEmployee($divisions, $filtered, $line, $division_type, $products, $from, $to, $period, $fromYear));
            }
        }

        return response()->json([
            'data' => $data,
            'fields' => $fields,
        ]);
    }


    private function detailsPerEmployee($belowDivisions, $users, $line, $division_type, $products, $from, $to, $period, $fromYear)
    {
        $belowDivisions = [];
        $users->each(function ($user) use ($division_type, $line, &$belowDivisions, $from, $to) {
            $belowDivisions = [...$belowDivisions, ...$user->allBelowDivisions($line, $from, $to)->where('division_type_id', '=', $division_type)->where('is_kol', 0)
                ->unique('id')->pluck('id')->toArray()];
        });
        $targets = collect([]);
        foreach ($period as $month) {
            $data = collect([]);
            $data = TargetDetails::select(
                'lines.id as line_id',
                'lines.name as line',
                'line_divisions.name as division',
                DB::raw('IFNULL(group_concat(distinct crm_users.fullname),"") as employee'),
                DB::raw('IFNULL(group_concat(distinct crm_users.emp_code),"") as emp_code'),
                DB::raw('IFNULL(crm_target_details.product_id,"") as product_id'),
                DB::raw('DATE_FORMAT(crm_target_details.date, "%Y-%m-%d") as target_date'),
                DB::raw('IFNULL(crm_products.name,"") as product'),
                DB::raw('ROUND(IFNULL(crm_target_details.value, 0), 2) as target_value'),
                'target_details.target as unit',
            )
                ->selectRaw('CAST(IFNULL(MAX(crm_product_prices.avg_price) * crm_target_details.target, 0) AS DECIMAL(10,0)) as value_price')
                ->leftJoin('products', 'target_details.product_id', 'products.id')
                ->leftJoin(
                    'product_prices',
                    fn($join) => $join->on('products.id', 'product_prices.product_id')
                        ->where('product_prices.from_date', '<=', $month)
                        ->whereNull('product_prices.deleted_at')
                        ->where(fn($q) => $q->where('product_prices.to_date', '>=', (string)Carbon::parse($month)->endOfMonth()->toDateString())
                            ->orWhere('product_prices.to_date', null))
                )
                ->leftJoin('lines', 'target_details.line_id', 'lines.id')
                ->leftJoin('line_divisions', 'target_details.div_id', 'line_divisions.id')
                ->leftJoin('line_users_divisions', 'line_divisions.id', 'line_users_divisions.line_division_id')
                ->leftJoin('users', function ($join) use ($from, $to) {
                    $join->on('line_users_divisions.user_id', 'users.id')
                        ->where('line_users_divisions.from_date', '<=', $from)
                        ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                            ->orWhere('line_users_divisions.to_date', '>=', Carbon::parse($to)->toDateString()));
                })
                ->where(DB::raw("(DATE_FORMAT(date,'%m'))"), $month->format('m'))->whereYear('date', $fromYear)
                ->whereIntegerInRaw('products.id', $products)
                ->whereIntegerInRaw('target_details.div_id', $belowDivisions)
                ->groupBy(
                    'target_details.id',
                    'target_details.line_id',
                    'target_details.div_id',
                    'target_details.product_id',
                    'target_details.date',
                )->get();
            $targets = $targets->merge($data);
        }
        return $targets;
    }


    private function detailsPerDivision($belowDivisions, $products, $from, $to, $period, $fromYear)
    {
        $targets = TargetDetails::select(
            'target_details.line_id',
            'lines.name as line',
            'target_details.div_id',
            'line_divisions.name as division',
            DB::raw('IFNULL(crm_target_details.brick_id,"") AS brick_id'),
            DB::raw('IFNULL(crm_bricks.name,"") AS brick'),
            'target_details.product_id',
            'products.name as product',
            DB::raw('ROUND(IFNULL(crm_target_details.value, 0), 2) as target_value'),
            'target_details.target as unit',
            DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_users.fullname), "") as employee'),
            DB::raw('IFNULL(GROUP_CONCAT(DISTINCT crm_users.emp_code), "") as emp_code'),
            DB::raw('DATE_FORMAT(crm_target_details.date, "%Y-%m-%d") as target_date')
        )
            ->selectRaw('CAST(IFNULL(MAX(crm_product_prices.avg_price) * crm_target_details.target, 0) AS DECIMAL(10,0)) as value_price')
            ->leftJoin('products', 'target_details.product_id', 'products.id')
            ->leftJoin(
                'product_prices',
                fn($join) => $join->on('products.id', 'product_prices.product_id')
                    ->where('product_prices.from_date', '<=', $from)
                    ->whereNull('product_prices.deleted_at')
                    ->where(fn($q) => $q->where('product_prices.to_date', '>=', $to)
                        ->orWhere('product_prices.to_date', null))
            )
            ->leftJoin('lines', 'target_details.line_id', 'lines.id')
            ->leftJoin('bricks', 'target_details.brick_id', 'bricks.id')
            ->leftJoin('line_divisions', 'target_details.div_id', 'line_divisions.id')
            ->leftJoin('line_users_divisions', 'line_divisions.id', 'line_users_divisions.line_division_id')
            ->leftJoin('users', function ($join) use ($from, $to) {
                $join->on('line_users_divisions.user_id', 'users.id')
                    ->where('line_users_divisions.from_date', '<=', $from)
                    ->where(fn($q) => $q->where('line_users_divisions.to_date', '=', null)
                        ->orWhere('line_users_divisions.to_date', '>=', Carbon::parse($to)->toDateString()));
            })
            ->whereBetween('target_details.date', [$from, $to])
            ->whereIntegerInRaw('products.id', $products)
            ->whereIntegerInRaw('target_details.div_id', $belowDivisions)
            ->groupBy(
                'target_details.id',
                'target_details.line_id',
                'target_details.div_id',
                'target_details.product_id',
                'target_details.date',
                'target_details.brick_id',
            )->get();
        return $targets;
    }


    public function getFields($perDivOrUserFilter)
    {
        $fields = [];
        if ($perDivOrUserFilter == 1) {
            $fields = collect(["line", "division", "employee", "emp_code", "brick", "target_date"]);
        } else {
            $fields = collect(["line", "employee", "emp_code", "target_date"]);
        }
        $fields = $fields->merge([
            "product",
            "unit",
            "target_value",
            "value_price"
        ]);
        return $fields;
    }
}

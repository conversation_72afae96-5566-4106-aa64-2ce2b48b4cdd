<?php

namespace App\Http\Controllers;

use App\ActualVisit;
use App\Approvable;
use App\ApprovablePlanable;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Helpers\LogActivity;
use App\Http\Requests\PlanableApprovableRequest;
use App\Models\AccountRequest;
use App\Models\ChangePlan;
use App\Models\CommercialRequest\CommercialBill;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\Expenses\Expense;
use App\Models\Material;
use App\Models\NewAccountDoctor;
use App\Planable;
use App\PlanVisit;
use App\Vacation;
use App\Models\PV\PV;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class GeneralApprovalSettingController extends ApiController
{
    public function __construct(private ?Collection $types = null)
    {
        $this->types = collect([
            ['id' => 1, 'type' => PlanVisit::class, 'name' => 'Plan'],
            ['id' => 2, 'type' => ActualVisit::class, 'name' => 'Actual'],
            ['id' => 3, 'type' => Vacation::class, 'name' => 'Vacation'],
            ['id' => 4, 'type' => CommercialRequest::class, 'name' => 'Commercial'],
            ['id' => 5, 'type' => Expense::class, 'name' => 'Expense'],
            ['id' => 6, 'type' => NewAccountDoctor::class, 'name' => 'Active & Inactive'],
            ['id' => 7, 'type' => AccountRequest::class, 'name' => 'Account Request'],
            ['id' => 8, 'type' => CommercialBill::class, 'name' => 'Commercial Bill'],
            ['id' => 9, 'type' => Material::class, 'name' => 'Material'],
            ['id' => 10, 'type' => ChangePlan::class, 'name' => 'Change Plan'],
            ['id' => 11, 'type' => PV::class, 'name' => 'PV'],
        ]);
    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $planables = Approvable::with('planables.planable')->get()->pluck('planables')->collapse()
            ->map(function ($planable) {
                return [
                    'id' => $planable->id,
                    'line' => $planable->line?->name ?? '',
                    'name' => $planable->planable->name,
                    'type' => $planable->pivot->request_type,
                    'actions' => ''
                ];
            })->unique(function ($item) {
                return [$item['line'] . $item['name'] . $item['type']];
            });
        return $this->respond(["planables" => $planables, "types" => $this->types]);
    }

    public function requestTypes()
    {
        return response()->json(['types' => $this->types]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = collect($request->approvables)
            ->mapWithKeys(function ($item) use ($request) {
                return [$item["approvable_id"] => [...$item, 'request_type' => $request->requestType, 'updated_at' => now(), 'created_at' => now()]];
            });
        $planable = Planable::findOrFail($request->planable);
        $planable->approvables()->wherePivot('request_type', $request->requestType)->sync($data);
        return $this->respond();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $planables = Planable::find()->approvables->map(function ($approvable) {
            return [
                'id' => $approvable->id,
                'required' => $approvable->pivot->required,
                'name' => $approvable->approvable->name,
            ];
        });
        return $this->respond($planables);
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  App\Http\Requests\PlanableApprovableRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(PlanableApprovableRequest $request, $id)
    {
        $data = collect($request->approvables)
            ->mapWithKeys(function ($item) use ($request) {
                return [$item["approvable_id"] => [...$item, 'request_type' => $request->requestType, 'updated_at' => now(), 'created_at' => now()]];
            });
        $planable = Planable::findOrFail($id);
        $planable->approvables()->wherePivot('request_type', $request->requestType)->sync($data);

        LogActivity::addLog();
        return $this->respond($request->approvables);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        Planable::findOrFail($request->id)->approvables()->wherePivot('request_type', $request->type)->detach();
        LogActivity::addLog();
        return $this->respondSuccess();
    }

    public function getPlanableWithSettings($id, $type)
    {
        $planable = Planable::where('id', $id)->first();
        $all_approvables = $this->allApprovables($planable);
        return $this->respond(
            [
                'planable' =>  [
                    'id' => $planable->id,
                    'name' => $planable->planable->name,
                ],
                'line' => $planable->line,
                'all_approvables' => $all_approvables['approvables'],
                'countApprovables' => $all_approvables['countApprovables'],
                'approvables' => $this->getApprovables($planable, $type)
            ]
        );
    }

    public function getApprovables($planable, $type)
    {
        if ($type == 1) {
            $approvables = $planable->approvables()->wherePivot('request_type', PlanVisit::class);
        }
        if ($type == 2) {
            $approvables = $planable->approvables()->wherePivot('request_type', ActualVisit::class);
        }
        if ($type == 3) {
            $approvables = $planable->approvables()->wherePivot('request_type', Vacation::class);
        }
        if ($type == 4) {
            $approvables = $planable->approvables()->wherePivot('request_type', CommercialRequest::class);
        }
        if ($type == 5) {
            $approvables = $planable->approvables()->wherePivot('request_type', Expense::class);
        }
        if ($type == 6) {
            $approvables = $planable->approvables()->wherePivot('request_type', NewAccountDoctor::class);
        }
        if ($type == 7) {
            $approvables = $planable->approvables()->wherePivot('request_type', AccountRequest::class);
        }
        if ($type == 8) {
            $approvables = $planable->approvables()->wherePivot('request_type', CommercialBill::class);
        }
        if ($type == 9) {
            $approvables = $planable->approvables()->wherePivot('request_type', Material::class);
        }
        if ($type == 10) {
            $approvables = $planable->approvables()->wherePivot('request_type', ChangePlan::class);
        }
        if ($type == 11) {
            $approvables = $planable->approvables()->wherePivot('request_type', PV::class);
        }
        $approvables = $approvables->get()->map(function ($approvable) {
            return [
                'approvable_id' => $approvable->id,
                'required' => $approvable->pivot->required == 1 ? true : false,
                'show_data' => $approvable->pivot->show_data == 1 ? true : false,
                'request_type' => $approvable->pivot->request_type,
                'flow' => $approvable->pivot->flow,
                'num_days' => $approvable->pivot->num_days
            ];
        });
        return $approvables;
    }

    public function allApprovables($planable)
    {
        $data = collect([]);
        if ($planable->planable_type == DivisionType::class) {
            $divType = DivisionType::find($planable->planable_id);
            $data = $this->parents($divType, $data)->pluck('id');
        }
        $approvables = Approvable::where('line_id', '=', $planable->line_id);
        if (count($data) >= 0 && $planable->planable_type == DivisionType::class) {
            $approvables = $approvables->whereIn('approvable_id', $data);
        }
        $countApprovables = [];
        $approvables = $approvables->get();
        $positionApprovals = Approvable::where('line_id', '=', null)->get();
        $approvables = $approvables->merge($positionApprovals);
        $approvables = $approvables->map(function ($approvable, $index) use (&$countApprovables) {
            array_push($countApprovables, $index + 1);
            return [
                'id' => $approvable->id,
                'name' => $approvable->approvable->name,
            ];
        });
        return array(
            'approvables' => $approvables,
            'countApprovables' => $countApprovables
        );
    }

    public function parents(DivisionType $divisionType, Collection $divisionTypeParents): Collection
    {
        $parent = $divisionType->parent;
        if ($divisionType->parent) {
            $divisionTypeParents = $divisionTypeParents->push($parent);
            $this->parents($parent, $divisionTypeParents);
        }
        $sortedDivisionTypes = DivisionType::whereIn('id', $divisionTypeParents->pluck('id'))->orderBy('sort', 'ASC')->get();
        return collect($sortedDivisionTypes);
    }
}

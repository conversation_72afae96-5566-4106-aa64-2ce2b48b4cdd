<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountLines;
use App\ActualVisit;
use App\Brick;
use App\Classes;
use App\DivisionType;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Giveaway;
use App\Helpers\LogActivity;
use App\Line;
use App\Models\DoubleVisitLocation;
use App\OwPlanVisit;
use App\PlanVisit;
use App\PlanVisitDetails;
use App\Product;
use App\Services\DifferenceBetweenTwoCoordinates;
use App\Speciality;
use App\User;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class VisitsReportController extends ApiController
{
    public function getLineData(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = Line::whereIntegerInRaw('id', $request->lines)->get();
        $divisions = collect([]);
        foreach ($lines as $line) {
            $divisions = $divisions->merge($user->userDivisions($line));
        }

        $last_level_divisions = collect([]);
        $division_type = DivisionType::where('last_level', '=', 1)->get('id');
        foreach ($lines as $line) {
            $last_level_divisions = $last_level_divisions->push(
                $user->userDivisions($line)
                    ->where('division_type_id', $division_type->first()->id)
            );
        }
        $last_level_divisions = $last_level_divisions->collapse()->unique('id')->values();
        // throw new CrmException($divisions);
        // $divisions = LineDivision::whereIntegerInRaw('line_id', $request->lines)->where('from_date', '<=', (string)Carbon::now())
        //     ->where(fn ($q) => $q->where('to_date', '=', null)->orWhere('to_date', '>=', (string)Carbon::now()))->get();
        $users = collect([]);
        if ($request->view == 'Active' || $request->view == null) {
            $users = $user->belowUsersOfAllLinesWithPositions($lines, 'Active');
        } else {
            $users = $user->belowUsersOfAllLinesWithPositions($lines, 'Inactive');
        }

        if ($request->is_mr) {
            $users = User::select([
                DB::raw('distinct crm_users.id as id'),
                'users.fullname as fullname',
                'lines.name as line',
            ])
                ->leftJoin('line_users', function ($join) {
                    $join->on('users.id', 'line_users.user_id')
                        ->where(
                            fn($q) => $q->where('line_users.to_date', '>=', (string)\Illuminate\Support\Carbon::now())
                                ->orWhere('line_users.to_date', null)
                        );
                })
                ->leftJoin('lines', 'line_users.line_id', 'lines.id')
                ->leftJoin('line_users_divisions', function ($join) {
                    $join->on('users.id', 'line_users_divisions.user_id')
                        ->where(
                            fn($q) => $q->where('line_users_divisions.to_date', '>=', (string)Carbon::now())
                                ->orWhere('line_users_divisions.to_date', null)
                        );
                })
                ->leftJoin('line_divisions', 'line_users_divisions.line_division_id', 'line_divisions.id')
                ->leftJoin('division_types', 'line_divisions.division_type_id', 'division_types.id')
                ->where('status', 'active')
                ->whereIntegerInRaw('lines.id', $lines->pluck('id')->toArray())
                ->where('division_types.last_level', 1)
                ->where("line_divisions.is_kol", 0)
                ->whereIn("users.id", $user->pluck("id")->toArray())
                ->get();
        }


        $specialities = Speciality::whereHas(
            'lineSpecialities',
            fn($q) => $q->whereIntegerInRaw('line_specialities.line_id', $request->lines)
                ->where('line_specialities.from_date', '<=', now())
                ->where(
                    fn($q) => $q->where('line_specialities.to_date', '>', (string)Carbon::now())
                        ->orWhere('line_specialities.to_date', null)
                )
        )->get();
        $bricks = Brick::whereHas(
            'lineBricks',
            fn($q) => $q->whereIntegerInRaw('line_bricks.line_id', $request->lines)->where('line_bricks.from_date', '<=', now())
                ->where(
                    fn($q) => $q->where('line_bricks.to_date', '>', (string)Carbon::now())
                        ->orWhere('line_bricks.to_date', null)
                )
        )->get();
        $products = Product::whereHas(
            'lineproducts',
            fn($q) => $q->whereIntegerInRaw('line_products.line_id', $request->lines)->where('line_products.from_date', '<=', now())
                ->where(
                    fn($q) => $q->where('line_products.to_date', '>', (string)Carbon::now())
                        ->orWhere('line_products.to_date', null)
                )
        )->get();
        $classes = Classes::whereHas(
            'lineClasses',
            fn($q) => $q->whereIntegerInRaw('line_classes.line_id', $request->lines)
        )->get();
        $giveaways = Giveaway::whereIntegerInRaw('line_id', $request->lines)->get();

        $distributors = Line::lineDistributors($lines->pluck('id'));

        return response()->json([
            'role' => $user,
            'divisions' => $divisions,
            'last_level_divisions' => $last_level_divisions,
            'users' => $users,
            'specialities' => $specialities,
            'bricks' => $bricks,
            'products' => $products,
            'giveaways' => $giveaways,
            'classes' => $classes,
            'distributors' => $distributors
        ]);
    }

    public function filterReport(Request $request)
    {
        $visit = $request->visitFilter;
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();
        $periodMonths = CarbonPeriod::create($from, '1 month', $to);
        // Get the months and format them as you need
        $months = [];
        foreach ($periodMonths as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $from->format('Y'),
        ];
        $actual_fields = collect([
            "id",
            "line",
            "division",
            // "manager_level",
            "employee",
            "emp_code",
            "account",
            "acc_code",
            "address",
            "brick",
            "doctor",
            "speciality",
            "acc_type",
            "pharmacy_type",
            "type_shift",
            "visit_shift",
            // "approved_by",
            "product",
            "brief",
            "brand",
            "comment",
            "product_samples",
            "product_order",
            "product_stock",
            "attachments"
        ]);
        $ow_fields = collect(["id", "employee", "type", "date", "shift", 'insertion_date', "notes", "ow_actions"]);
        $ow_plan_fields = collect(["s", "id", "employee", "type", "date", "shift", "notes"]);
        $data = match ($visit['actualProductData']) {
            1 => $actual_fields,
            2 => $actual_fields->push('follow'),
            3 => $actual_fields->push('mf')
        };
        $actual_fields = $actual_fields->merge([
            "v_feedback",
            "type",
            "manager",
            "sec_type",
            "visit_from",
            "start",
            "end",
            "duration_status",
            "duration",
            "insertion",
            "os_version",
            "device_brand",
            "os_type",
            "fake_gps",
            "ll",
            "lg",
            "map",
            "v_details",
            "actual_actions"
        ]);
        $plan_fields = collect(["s", "line", "division", "employee", "account", "brick", "doctor", "speciality", "acc_type", "shift", "clarification", "type", "related_emp", "date","insertion", "status", "approved_by", "plan_actions"]);

        /**@var User $user */
        $user = Auth::user();

        $data = match ($visit['visitType']) {
            2 => $this->getActualVisits($visit, $from, $to),
            3 => $this->getPlanOw($visit, $from, $to),
            4 => $this->getActualOw($visit, $from, $to),
            default => $this->getPlanVisits($user, $visit, $from, $to),
        };

        LogActivity::addLog();
        return response()->json([
            'visits' => $data,
            'dates' => $dates,
            'actual_fields' => $actual_fields->toArray(),
            'plan_fields' => $plan_fields,
            'ow_fields' => $ow_fields,
            'ow_plan_fields' => $ow_plan_fields,
        ]);
    }

    private function getActualVisits($visit, $from, $to)
    {
        if (empty($visit['users']) && empty($visit['divisions']) && empty($visit['bricks']))
            return [];
        $query = ActualVisit::select([
            'actual_visits.id as id',
            'actual_visits.line_id',
            'actual_visits.plan_id',
            'actual_visits.is_automatic',
            'actual_visits.div_id',
            'actual_visits.user_id',
            'actual_visits.ll',
            'actual_visits.lg',
            'actual_visits.acc_type_id',
            DB::raw("
                    CASE
                        WHEN crm_actual_visits.is_web_visit = 1 THEN 'Web'
                        WHEN crm_actual_visits.is_web_visit = 0 THEN 'App'
                        ELSE 'Unknown Status'
                    END as visit_from
                "),
            DB::raw("
                CASE
                    WHEN crm_actual_visits.is_fake_gps = 1 THEN 'Yes'
                    WHEN crm_actual_visits.is_fake_gps = 0 THEN 'No'
                    ELSE 'No'
                END as fake_gps
            "),
            DB::raw("
                CASE
                    WHEN crm_actual_visits.os_type = 1 THEN 'IOS'
                    WHEN crm_actual_visits.os_type = 0 THEN 'Android'
                    ELSE 'Web'
                END as os_type
            "),
            DB::raw('IFNULL(crm_actual_visits.os_version,"") as os_version'),
            DB::raw('IFNULL(crm_actual_visits.device_brand,"") as device_brand'),
            'actual_visits.account_id',
            'actual_visits.created_at as insertion',
            DB::raw('IFNULL(crm_actual_visits.visit_duration,"") as duration'),
            'actual_visits.visit_date as start',
            DB::raw('IFNULL(crm_actual_visits.end_visit_date,"") as end'),
            DB::raw('IFNULL(crm_bricks.name,"") as brick'),
            'employees.fullname as employee',
            DB::raw('IFNULL(crm_employees.emp_code,"") as emp_code'),
            'lines.name as line',
            'line_divisions.name as division',
            'accounts.name as account',
            DB::raw('IFNULL(crm_accounts.address,"") as address'),
            'accounts.id as account_id',
            DB::raw('IFNULL(crm_accounts.code,"") as acc_code'),
            DB::raw('IFNULL(crm_pharmacy_type_visits.name,"") as pharmacy_type'),
            'account_types.name as acc_type',
            'account_types.shift_id as acc_shift_id',
            'type_shifts.name as type_shift',
            // 'actual_shifts.name as visit_shift',
            // 'actual_visits.id as visit_shift_id',
            DB::raw('IFNULL(crm_actual_shifts.name,"") as visit_shift'),
            DB::raw('IFNULL(crm_actual_shifts.id,"") as visit_shift_id'),
            DB::raw("
                    CASE
                        WHEN crm_actual_visits.visit_status = 1 THEN 'False Time'
                        WHEN crm_actual_visits.visit_status = 0 THEN 'Accurate Duration'
                        ELSE 'Unknown Status'
                    END as duration_status
                "),
            DB::raw('IFNULL(group_concat(distinct crm_managers.fullname),"") as manager'),
            DB::raw('IFNULL(group_concat(distinct crm_products.name),"") as product'),
            DB::raw("group_concat(crm_products.name ,'(', crm_actual_visit_products.samples , ')') as product_samples"),
            DB::raw("group_concat(crm_products.name ,'(', crm_actual_visit_products.current_order , ')') as product_order"),
            DB::raw("group_concat(crm_products.name ,'(', crm_actual_visit_products.stock , ')') as product_stock"),
            DB::raw('IFNULL(group_concat(distinct crm_products.short_name),"") as brief'),
            DB::raw('IFNULL(group_concat(distinct crm_brands.name),"") as brand'),
            DB::raw('IFNULL(group_concat(distinct crm_actual_visit_products.notes),"") as comment'),
            DB::raw('IFNULL(group_concat(distinct crm_actual_visit_products.follow_up),"") as follow'),
            DB::raw('IFNULL(group_concat(distinct crm_actual_visit_products.market_feedback),"") as mf'),
            DB::raw('IFNULL(group_concat(crm_visit_feedbacks.notes),"") as v_feedback'),
            DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
            DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
            DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
            'visit_types.name as type',
            DB::raw('IFNULL(crm_double_visit_types.name,"") as sec_type'),
            'specialities.id as speciality_id',
            DB::raw('IFNULL(group_concat(distinct crm_attachments.path),"") as attachments'),
        ])
            ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
            ->leftJoin('users as employees', 'actual_visits.user_id', 'employees.id')
            ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->leftJoin('shifts as type_shifts', 'account_types.shift_id', 'type_shifts.id')
            ->leftJoin('shifts as actual_shifts', 'actual_visits.shift_id', 'actual_shifts.id')
            ->leftJoin('pharmacy_type_visits', 'actual_visits.pharmacy_type_id', 'pharmacy_type_visits.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
            ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
            ->leftJoin('double_visit_types', 'actual_visits.double_visit_type_id', 'double_visit_types.id')
            ->leftJoin(
                'actual_visits_managers',
                function ($join) {
                    $join->on('actual_visits.id', '=', 'actual_visits_managers.visit_id');
                }
            )
            ->leftJoin(
                'attachments',
                function ($join) {
                    $join->on('actual_visits.id', '=', 'attachments.attachable_id');
                    $join->where('attachments.attachable_type', ActualVisit::class);
                }
            )
            ->leftJoin('users as managers', 'actual_visits_managers.user_id', 'managers.id')
            ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            ->leftJoin('visit_feedbacks', 'actual_visit_products.vfeedback_id', 'visit_feedbacks.id')
            ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->leftJoin('brands', 'actual_visit_products.brand_id', 'brands.id')
            ->where('actual_visits.deleted_at', '=', null)
            ->whereBetween('visit_date', [$from, $to]);

        match ($visit['status']) {
            1 => $query,
            2 => $query->where('plan_visit_details.approval', 1),
            3 => $query->whereNull('plan_visit_details.approval'),
        };
        match ($visit['actualProductData']) {
            1 => $query,
            2 => $query->whereNotNull('actual_visit_products.follow_up'),
            3 => $query->whereNotNull('actual_visit_products.market_feedback'),
        };
        $data = match ($visit['actualData']) {
            1 => $query,
            2 => $query->whereNull('actual_visits.plan_id'),
            3 => $query->whereNotNull('actual_visits.plan_id'),
        };
        if (!empty($visit['divisions'])) {
            $query->whereIntegerInRaw('actual_visits.div_id', $visit['divisions']);
        }
        if (!empty($visit['bricks'])) {
            $query->whereIntegerInRaw('actual_visits.brick_id', $visit['bricks']);
        }
        if (!empty($visit['users'])) {
            $query->whereIntegerInRaw('actual_visits.user_id', $visit['users']);
        }
        if (!empty($visit['lines'])) {
            $query->whereIntegerInRaw('actual_visits.line_id', $visit['lines']);
        }
        if (!empty($visit['types'])) {
            $query->whereIntegerInRaw('actual_visits.acc_type_id', $visit['types']);
        }
        if (!empty($visit['shifts'])) {
            $query->whereIntegerInRaw('account_types.shift_id', $visit['shifts']);
        }
        if (!empty($visit['specialities'])) {
            $query->whereIntegerInRaw('specialities.id', $visit['specialities']);
        }
        if (!empty($visit['visit_types'])) {
            $query->whereIntegerInRaw('visit_types.id', $visit['visit_types']);
        }
        if (!empty($visit['another_types'])) {
            $query->whereIntegerInRaw('actual_visits.double_visit_type_id', $visit['another_types']);
        }
        return $query
            ->groupBy(
                "actual_visits.id",
            )
            ->get();
    }

    private function getPlanOw($visit, $from, $to)
    {
        $query = DB::table('ow_plan_visits')
            ->select(
                'ow_plan_visits.id',
                'users.fullname as employee',
                'ow_plan_visits.user_id',
                DB::raw('IFNULL(crm_shifts.name,"Full Day") AS shift'),
                DB::raw('IFNULL(crm_ow_plan_visits.notes,"") AS notes'),
                'ow_plan_visits.day as date',
                'office_work_types.name as type',
            )
            ->leftJoin('office_work_types', 'ow_plan_visits.ow_type_id', '=', 'office_work_types.id')
            ->leftJoin('users', 'ow_plan_visits.user_id', '=', 'users.id')
            ->leftJoin('shifts', 'ow_plan_visits.shift_id', '=', 'shifts.id')
            ->where('ow_plan_visits.deleted_at', '=', null)
            ->whereBetween('ow_plan_visits.day', [$from, $to]);

        if (!empty($visit['users'])) {
            $query->whereIntegerInRaw('ow_plan_visits.user_id', $visit['users']);
        }
        if (!empty($visit['shifts'])) {
            $query->where(fn($q) => $q->whereIntegerInRaw('ow_plan_visits.shift_id', $visit['shifts'])->orWhereNull('shift_id'));
        }
        return $query->orderBy('ow_plan_visits.day', 'DESC')->get();
    }

    private function getActualOw($visit, $from, $to)
    {
        $query = DB::table('ow_actual_visits')
            ->select(
                'ow_actual_visits.id',
                'users.fullname as employee',
                'ow_actual_visits.user_id',
                DB::raw('IFNULL(crm_shifts.name,"Full Day") AS shift'),
                DB::raw('IFNULL(crm_ow_actual_visits.notes,"") AS notes'),
                'ow_actual_visits.date',
                'office_work_types.name as type',
                'ow_actual_visits.created_at as insertion_date',
            )
            ->leftJoin('office_work_types', 'ow_actual_visits.ow_type_id', '=', 'office_work_types.id')
            ->leftJoin('users', 'ow_actual_visits.user_id', '=', 'users.id')
            ->leftJoin('shifts', 'ow_actual_visits.shift_id', '=', 'shifts.id')
            ->where('ow_actual_visits.deleted_at', '=', null)
            ->whereBetween('ow_actual_visits.date', [$from, $to]);

        if (!empty($visit['users'])) {
            $query->whereIntegerInRaw('ow_actual_visits.user_id', $visit['users']);
        }
        if (!empty($visit['shifts'])) {
            $query->where(fn($q) => $q->whereIntegerInRaw('ow_actual_visits.shift_id', $visit['shifts'])->orWhereNull('shift_id'));
        }

        return $query->orderBy('date', 'DESC')->get();
    }

    private function getPlanVisits($user, $visit, $from, $to)
    {
        $query = PlanVisit::with('details')->when(!empty($visit['shifts']), fn($q) => $q->whereIntegerInRaw("shift_id", $visit['shifts']))
            ->whereBetween('visit_date', [$from, $to]);
        if (!$user->hasRole('admin')) {
            $visit['users'][] = auth()->id();
        }
        if (!empty($visit['lines'])) {
            $query->whereIntegerInRaw('line_id', $visit['lines']);
        }
        if (!empty($visit['divisions'])) {
            $query->whereIntegerInRaw('div_id', $visit['divisions']);
        }

        if (!empty($visit['users'])) {
            $query->whereIntegerInRaw('user_id', $visit['users']);
        }
        if (!empty($visit['types'])) {
            $query->whereHas(
                'account',
                function ($q) use ($visit) {
                    $q->whereIntegerInRaw('type_id', $visit['types']);
                }
            );
        }
        if (!empty($visit['specialities'])) {
            $query->whereHas(
                'doctor',
                function ($q) use ($visit) {
                    $q->whereIntegerInRaw('speciality_id', $visit['specialities']);
                }
            );
        }
        return $query->orderBy('visit_date', 'DESC')->get()->map(function ($visit) {
            $shift = $visit->account?->type?->shift;
            return [
                'id' => $visit->id,
                'insertion' => Carbon::parse($visit->created_at)->toDateTimeString(),
                'date' => $visit->visit_date,
                'clarification' => $visit->clarification ?? '',
                'line' => $visit->line->name,
                'division' => $visit->division->name ? $visit->division->name : '',
                'employee' => $visit->user->fullname ? $visit->user->fullname : '',
                'emp_code' => $visit->user->emp_code ? $visit->user->emp_code : '',
                'account' => $visit->account->name,
                'brick' => $visit->account->accountlines()->where('line_id', $visit->line->id)->where('line_division_id', $visit->division->id)->first()?->brick?->name ?? '',
                'doctor' => $visit->doctor ? $visit->doctor->name : '',
                'speciality' => $visit->doctor?->speciality?->name ?? '',
                'acc_type' => $visit->account->type->name,
                'shift' => $shift ? $shift->name : '',
                'acc_shift_id' => $shift ? $shift->id : '',
                'type' => $visit->visitType->name,
                'related_emp' => $visit->visit_type == 2 ? $visit->relatedPlan?->user?->fullname : '',
                'status' => $visit->details->approval === null ? 'Pending' : ($visit->details?->approval === 1 ? 'Approved' : 'Disapproved'),
                'approved_by' => $visit->details()
                    ->with('user')
                    ->get()
                    ->map(
                        fn($detail) => $detail->user
                            ? "{$detail->user->fullname} ({$detail->updated_at->format('Y-m-d H:i:s')})"
                            : ''
                    )
                    ->filter() // Remove empty values
                    ->implode(' , ')
            ];
        });
    }

    public function deletePlans(Request $request)
    {
        $planIds = collect($request->plans);
        if (!empty($planIds)) {
            PlanVisitDetails::whereIn('visitable_id', $planIds)->where('visitable_type', PlanVisit::class)->delete();
            PlanVisit::whereIn('id', $planIds)->forceDelete();
        }
        return $this->respondSuccess();
    }

    public function deleteOwPlans(Request $request)
    {
        $planIds = collect($request->ows);
        if (!empty($planIds)) {
            PlanVisitDetails::whereIn('visitable_id', $planIds)->where('visitable_type', OwPlanVisit::class)->delete();
            OwPlanVisit::whereIn('id', $planIds)->forceDelete();
        }
        return $this->respondSuccess();
    }

    public function visitLocation(Request $request)
    {
        $visit = $request->visit;
        $doubleVisit = DoubleVisitLocation::where('visit_id', $visit['id'])->first();
        $location = [
            "id" => $visit['id'],
            "position" => [
                'visit_id' => $visit['id'],
                'account' => Account::find($visit['account_id'])->name,
                'doctor' => $visit['doctor_id'] ? Doctor::find($visit['doctor_id'])->name : '',
                'lat' => (float)$visit['ll'],
                'lng' => (float)$visit['lg'],
            ],
            "positionManager" => [
                'manager' => $doubleVisit?->user?->fullname,
                'account' => Account::find($visit['account_id'])->name,
                'doctor' => $visit['doctor_id'] ? Doctor::find($visit['doctor_id'])->name : '',
                'lat' => (float)$doubleVisit?->ll,
                'lng' => (float)$doubleVisit?->lg,
            ],
            "positionAccount" => [
                'account_id' => $visit['account_id'],
                'account' => Account::find($visit['account_id'])->name,
                'doctor' => $visit['doctor_id'] ? Doctor::find($visit['doctor_id'])->name : '',
                'lat' => (float)Account::find($visit['account_id'])?->accountlines()->where('line_division_id', $visit['div_id'])->first()->ll,
                'lng' => (float)Account::find($visit['account_id'])?->accountlines()->where('line_division_id', $visit['div_id'])->first()->lg,
            ],
        ];
        return $this->respond($location);
    }

    public function visitDetails(Request $request)
    // public function visitDetails($visit,$from,$to)
    {
        // $visit = $request->visitFilter;
        $item = $request->item;
        // $from = Carbon::parse($visit['fromDate'])->startOfDay();
        // $to = Carbon::parse($visit['toDate'])->endOfDay();
        $data = DB::table('actual_visits')->select(
            'actual_visits.id',
            'giveaways.name as giveaway',
            'actual_visit_giveaways.units as units',
            DB::raw('IFNULL(crm_giveaways.name,"") as giveaway'),
            DB::raw('IFNULL(crm_actual_visit_giveaways.units,"") as units'),
            DB::raw('IFNULL(crm_products.name,"") as product'),
            DB::raw('IFNULL(crm_product_messages.message,"") as message'),
            DB::raw('IFNULL(crm_visit_feedbacks.notes,"") as v_feedback'),
            DB::raw('IFNULL(crm_actual_visit_products.follow_up,"") as follow_up'),
            DB::raw('IFNULL(crm_actual_visit_products.notes,"") as comment'),
            DB::raw('IFNULL(crm_actual_visit_products.samples,"") as samples'),
            DB::raw('IFNULL(crm_actual_visit_products.market_feedback,"") as m_feedback'),
            DB::raw('IFNULL(crm_actual_double_feedbacks.feedback,"") as manager_feedback'),
            'brands.name as brand',
        )
            ->leftJoin('actual_visit_products', 'actual_visits.id', 'actual_visit_products.visit_id')
            ->leftJoin('products', 'actual_visit_products.product_id', 'products.id')
            ->leftJoin('brands', 'actual_visit_products.brand_id', 'brands.id')
            ->leftJoin('visit_feedbacks', 'actual_visit_products.vfeedback_id', 'visit_feedbacks.id')
            ->leftJoin('product_messages', 'actual_visit_products.message_id', 'product_messages.id')
            ->leftJoin('actual_visit_giveaways', 'actual_visits.id', 'actual_visit_giveaways.visit_id')
            ->leftJoin('giveaways', 'actual_visit_giveaways.giveaway_id', 'giveaways.id')
            ->leftJoin('actual_double_feedbacks', 'actual_visits.id', 'actual_double_feedbacks.visit_id')
            ->whereNull('actual_visits.deleted_at')
            ->whereNull('actual_visit_products.deleted_at')
            ->whereNull('actual_visit_giveaways.deleted_at')
            ->whereNull('products.deleted_at')
            ->whereNull('brands.deleted_at')
            ->whereNull('product_messages.deleted_at')
            ->whereNull('giveaways.deleted_at')
            ->whereNull('visit_feedbacks.deleted_at')
            ->whereNull('actual_double_feedbacks.deleted_at')
            // ->where('actual_visits.id', $visit->id)
            ->where('actual_visits.id', $item['id'])
            // ->whereBetween('visit_date', [$from, $to])
            ->get();
        return $this->respond($data);
        // return $data;
    }

    public function getEmployeesOrDivisions(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = Line::whereIn('id', $request->lines)->get();
        $isActive = $request->empStatus === 'active';
        $users = $user->belowUsersOfAllLinesWithPositions($lines, $isActive);
        return $this->respond($users);
    }

    public function gpsVisitFilterReport(Request $request)
    {
        $visit = $request->visitFilter;
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();
        $actual_fields = collect(["id", "Line", "Division", "Employee", "Emp_code", "Account", "Brick", "Doctor", "Speciality", "Acc_type", "Date", "Devition", "Map", "Status"]);

        $user = Auth::user();
        if (!empty($visit['users']) || !empty($visit['divisions']) || !empty($visit['bricks'])) {
            $data = ActualVisit::select(
                'actual_visits.id as id',
                'actual_visits.line_id',
                'actual_visits.div_id',
                'actual_visits.user_id',
                'actual_visits.ll',
                'actual_visits.lg',
                'actual_visits.acc_type_id',
                'actual_visits.account_id',
                'actual_visits.created_at as insertion',
                'actual_visits.visit_date as date',
                DB::raw('IFNULL(group_concat(distinct crm_bricks.name),"") as brick'),
                'employees.fullname as employee',
                'employees.emp_code as emp_code',
                'lines.name as line',
                'line_divisions.name as division',
                'accounts.name as account',
                'accounts.id as account_id',
                'account_types.name as acc_type',
                'account_types.shift_id as acc_shift_id',
                DB::raw('IFNULL(crm_doctors.name,"") as doctor'),
                DB::raw('IFNULL(crm_doctors.id,"") as doctor_id'),
                DB::raw('IFNULL(crm_specialities.name,"") as speciality'),
                'visit_types.name as type',
                'specialities.id as speciality_id',
                'account_type_distances.distance'
            )
                ->leftJoin('lines', 'actual_visits.line_id', 'lines.id')
                ->leftJoin('users as employees', 'actual_visits.user_id', 'employees.id')
                ->leftJoin('line_divisions', 'actual_visits.div_id', 'line_divisions.id')
                ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
                ->leftJoin('bricks', 'actual_visits.brick_id', 'bricks.id')
                ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
                ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
                ->leftJoin('specialities', 'doctors.speciality_id', 'specialities.id')
                ->leftJoin('visit_types', 'actual_visits.visit_type_id', 'visit_types.id')
                ->leftJoin('account_type_distances', 'account_types.id', 'account_type_distances.type_id')
                ->leftJoin(
                    'plan_visit_details',
                    function ($join) {
                        $join->on('actual_visits.id', '=', 'plan_visit_details.visitable_id');
                        $join->where('plan_visit_details.visitable_type', 'App\ActualVisit');
                    }
                )
                ->where('actual_visits.deleted_at', '=', null)
                ->whereBetween('visit_date', [$from, $to]);


            if (!empty($visit['divisions'])) {
                $data = $data->whereIntegerInRaw('actual_visits.div_id', $visit['divisions']);
            }
            if (!empty($visit['bricks'])) {
                $data = $data->whereIntegerInRaw('actual_visits.brick_id', $visit['bricks']);
            }
            if (!empty($visit['users'])) {
                $data = $data->whereIntegerInRaw('actual_visits.user_id', $visit['users']);
            }
            if (!empty($visit['lines'])) {
                $data = $data->whereIntegerInRaw('actual_visits.line_id', $visit['lines']);
            }
            if (!empty($visit['types'])) {
                $data = $data->whereIntegerInRaw('actual_visits.acc_type_id', $visit['types']);
            }
            if (!empty($visit['specialities'])) {
                $data = $data->whereIntegerInRaw('specialities.id', $visit['specialities']);
            }

            $data = $data
                ->groupBy(
                    "actual_visits.id",
                    "plan_visit_details.approval",
                    'account_type_distances.id',
                )
                ->get();
            if (count($data) == 0) {
                throw new Exception('There is no actual visits');
            }
        }
        if (count($data) == 0) {
            throw new Exception('There is no actual visits');
        }


        $data = $data->map(function ($visit) {
            $accountLine = AccountLines::select('ll', 'lg', 'visit_id')->where('line_id', $visit->line_id)->where('account_id', $visit['account_id'])
                ->where('line_division_id', $visit->div_id)->first();

            if ($visit->ll && $visit->lg) {
                $deviation = (int)(new DifferenceBetweenTwoCoordinates)->distanceBetweenTwoCoordinates($visit->ll, $visit->lg, $accountLine->ll, $accountLine->lg, 'M');
                $status = $visit->id == $accountLine->visit_id ? 'First Visit' : ($visit->distance > $deviation ? 'Near' : 'Far');
            } else {
                $status = 'No Location';
                $deviation = '';
            }
            return [
                'id' => $visit->id,
                'Line' => $visit->line,
                'Division' => $visit->division,
                'div_id' => $visit->div_id,
                'Employee' => $visit->employee,
                'Emp_code' => $visit->emp_code,
                'Account' => $visit->account,
                'account_id' => $visit->account_id,
                'Brick' => $visit->brick,
                'Doctor' => $visit->doctor,
                'doctor_id' => $visit->doctor_id,
                'Speciality' => $visit->speciality,
                'Acc_type' => $visit->acc_type,
                'll' => $visit?->ll,
                'lg' => $visit?->lg,
                'Date' => $visit->date,
                'Devition' => $deviation,
                'Map' => '',
                'Status' => $status,
                // 'Status' => $visit->id == $accountLine->visit_id ? 'First Visit' : ($visit->distance > $deviation ? 'Near' : 'Far'),
            ];
        });


        // throw new CrmException(gettype($data));


        if (!empty($visit['deviation_num'])) {
            if ($visit['deviation_num'] == '0m to 200m') {
                $data = $data->where('Devition', '<', 201);
                // throw new CrmException(gettype($data));

                // throw new CrmException($data);

            } elseif ($visit['deviation_num'] == '201m to 500m') {
                $data = $data->where('Devition', '<', 501)->where('Devition', '>', 200);
                // throw new CrmException($data);

            } elseif ($visit['deviation_num'] == '501m to 1000m') {
                $data = $data->where('Devition', '<', 1001)->where('Devition', '>', 500);
                // throw new CrmException($data);

            } elseif ($visit['deviation_num'] == 'More than 1000m') {
                $data = $data->where('Devition', '>', 1000);
            } else {
                return $data;
            }
        }
        // throw new CrmException($data);


        LogActivity::addLog();
        return response()->json([
            'visits' => $data->values(),
            'actual_fields' => $actual_fields->toArray(),
        ]);
    }
}

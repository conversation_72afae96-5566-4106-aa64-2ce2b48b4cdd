<?php

namespace App\Http\Controllers;

use App\DivisionType;
use App\Exceptions\CrmException;
use App\Helpers\Notifications\NotificationHelper;
use App\Models\ApprovalFlowUser;
use App\Models\ApprovalSetting;
use App\Models\ChangePlan;
use App\Models\PV\PV;
use App\Notifications\ChangePlanApprovedNotification;
use App\PlanVisit;
use App\PlanVisitDetails;
use App\Position;
use App\Reason;
use App\Reasonable;
use App\Services\PlanService;
use App\User;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PvApprovalController extends ApiController
{
    public function getChangePlans(Request $request)
    {
        $from_date = null;
        $to_date = null;
        $reasons = Reason::where('request_type', PV::class)->get();
        $pvs = collect([]);
        $from_date = Carbon::parse($request->from_date)->startOfDay();
        $to_date = Carbon::parse($request->to_date)->endOfDay();
        $pvs = PV::select(
            'p_v_s.id as id',
            'p_v_s.visit_id as visit_id',
            'pv_modules.name as module',
            'users.fullname as employee',
            DB::raw('group_concat(distinct crm_pv_modules.name) as module'),
            DB::raw('DATE_FORMAT(crm_p_v_s.submition_date, "%Y-%m-%d") as date'),
            DB::raw('group_concat(distinct crm_doctors.name) as doctor'),
            DB::raw('group_concat(distinct crm_qualifications.name) as qualification'),
            DB::raw('group_concat(distinct crm_doctor_information.phone) as phone'),
            DB::raw('group_concat(distinct crm_doctor_information.email) as email'),
            DB::raw('group_concat(distinct crm_doctor_information.other_info) as other_info'),
            DB::raw('group_concat(distinct crm_doctor_information.comment) as doctor_comment'),
            DB::raw('group_concat(distinct crm_patient_information.name) as patient'),
            DB::raw('group_concat(distinct crm_age_units.name) as age_unit'),
            DB::raw('group_concat(distinct crm_patient_information.exact_age) as exact_age'),
            DB::raw('group_concat(distinct crm_age_groups.name) as age_group'),
            DB::raw('group_concat(distinct crm_patient_sex.name) as sex'),
            DB::raw('group_concat(distinct crm_patient_information.medical_history) as medical_history'),
            DB::raw('group_concat(distinct crm_patient_information.taking_medications) as taking_medications'),
            DB::raw('group_concat(distinct crm_patient_information.comment) as patient_comment'),
            DB::raw('group_concat(distinct crm_event_information.narrative) as event'),
            DB::raw('group_concat(distinct crm_event_information.reaction_start_date) as reaction_start_date'),
            DB::raw('group_concat(distinct crm_event_information.reaction_end_date) as reaction_end_date'),
            DB::raw('group_concat(distinct crm_event_actions.name) as action'),
            DB::raw('group_concat(distinct crm_event_information.comment) as event_comment'),
            DB::raw('group_concat(distinct crm_products.id) as product_ids'),
            DB::raw('group_concat(distinct crm_products.name) as product'),
            // 'products.name as product',
            // 'drug_information.dosages as dosages',
            // 'drug_information.route_adminstration as route_adminstration',
            // 'drug_information.indication_for as indication_for',
            // 'drug_information.start_date as start_date',
            // 'drug_information.end_date as end_date',
            // 'drug_information.event_outcome as event_outcome',
            // DB::raw('case when crm_drug_information.retake = 0 then "No" else "Yes" end as retake'),
            // DB::raw('case when crm_drug_information.is_happened_again = 0 then "No" else "Yes" end as is_happened_again'),
            // 'drug_information.comment as drug_comment',
        )
            ->leftJoin('users', 'p_v_s.user_id', 'users.id')
            ->leftJoin('actual_visits', 'p_v_s.visit_id', 'actual_visits.id')
            ->leftJoin('doctors', 'actual_visits.account_dr_id', 'doctors.id')
            ->leftJoin('doctor_information', 'p_v_s.id', 'doctor_information.pv_id')
            ->leftJoin('qualifications', 'doctor_information.qualification_id', 'qualifications.id')
            ->leftJoin('drug_information', 'p_v_s.id', 'drug_information.pv_id')
            ->leftJoin('products', 'drug_information.product_id', 'products.id')
            ->leftJoin('patient_information', 'p_v_s.id', 'patient_information.pv_id')
            ->leftJoin('age_units', 'patient_information.age_unit_id', 'age_units.id')
            ->leftJoin('age_groups', 'patient_information.age_group_id', 'age_groups.id')
            ->leftJoin('patient_sex', 'patient_information.sex_id', 'patient_sex.id')
            ->leftJoin('event_information', 'p_v_s.id', 'event_information.pv_id')
            ->leftJoin('event_actions', 'event_information.action_id', 'event_actions.id')
            ->leftJoin('pv_modules', 'p_v_s.pv_module_id', 'pv_modules.id')
            ->whereBetween('submition_date', [$from_date, $to_date])
            ->whereIn('p_v_s.user_id', $request->users_id)
            ->groupBy('p_v_s.id', 'users.id')
            ->get();


        $approvalSetting = ApprovalSetting::where('key', 'pv_approval_center_flow')->value('value');
        // throw new CrmException($approvalSetting);
        /**@var User $authUser */
        $authUser = Auth::user();
        $approvalData = $authUser->userApprovals($from_date, $to_date);
        $lines = $approvalData['lines'];
        $linesAapprovables = $approvalData['linesAapprovables'];
        $required = 0;
        $dataFlow = '';
        $scanLevel = 1;
        $pvs = $pvs->filter(
            function (PV $pv) use ($from_date, $to_date, $lines, $linesAapprovables, $approvalSetting, $scanLevel, $authUser, &$required, $dataFlow) {
                if (!$approvalSetting || $approvalSetting == 'No') {
                    return is_null($pv->details?->approval);
                } else {
                    if (isNullable($pv->details?->approval)) {
                        $approvablesCountOnThisShit = $pv->details?->approvalFlows()->count();
                        $data = $authUser->approvalWidget($pv, $authUser, PV::class, $from_date, $to_date, $lines, $linesAapprovables);
                        $dataFlow = $data['linesDataFlow'];
                        $currentFlow = $dataFlow?->flow;
                        $required = $dataFlow?->required;
                        $showData = $dataFlow?->show_data;
                        $vacantCount = $data['vacantCount'];
                        $haveToApprove = $approvablesCountOnThisShit === $currentFlow - ($vacantCount + $scanLevel);
                        return $haveToApprove || $required || $showData;
                    }
                }
            }
        )
            ->map(function ($pv) use ($reasons, $required) {
                return [
                    'id' => $pv->id,
                    'visit_id' => $pv->visit_id,
                    'date' => $pv->date,
                    'module' => $pv->module,
                    'employee' => $pv->employee,
                    'doctor' => $pv->doctor,
                    'doctor_comment' => $pv->doctor_comment,
                    'patient' => $pv->patient,
                    'medical_history' => $pv->medical_history,
                    'taking_medications' => $pv->taking_medications,
                    'patient_comment' => $pv->patient_comment,
                    'event' => $pv->event,
                    'reaction_start_date' => $pv->reaction_start_date,
                    'reaction_end_date' => $pv->reaction_end_date,
                    'action' => $pv->action,
                    'event_comment' => $pv->event_comment,
                    'product' => $pv->product,
                    'approval' => $pv->details?->approval,
                    'required' => $required,
                    'visitable_type' => PV::class,
                    'reasons' => $reasons,
                    'reason_id' => null
                ];
            });
        $fields = [
            's',
            'id',
            'visit_id',
            'date',
            'module',
            'employee',
            'doctor',
            // 'qualification',
            // 'phone',
            // 'email',
            // 'other_info',
            'doctor_comment',
            'patient',
            // 'age_unit',
            // 'exact_age',
            // 'age_group',
            // 'sex',
            'medical_history',
            'taking_medications',
            'patient_comment',
            'event',
            'reaction_start_date',
            'reaction_end_date',
            'action',
            'event_comment',
            'product',
            // 'dosages',
            // 'route_adminstration',
            // 'indication_for',
            // 'start_date',
            // 'end_date',
            // 'event_outcome',
            // 'retake',
            // 'is_happened_again',
            // 'drug_comment',
            'actions'
        ];
        return $this->respond(['pvs' => $pvs->values(), 'pvFields' => $fields]);
    }

    public function accept(Request $request)
    {
        $approvalSetting = ApprovalSetting::where('key', 'change_plan_approval_center_flow')->value('value');
        $all_pvs = $request->pvs;

        if (empty($all_pvs)) {
            throw new Exception('No Pvs Found');
        }

        $authUser = Auth::user();
        $users = collect();
        $pvs = collect();
        DB::transaction(function () use ($all_pvs, $authUser, $users, $pvs, $approvalSetting) {
            foreach ($all_pvs as $pv) {
                /**
                 * @var PV $objPv
                 */
                $objPv = resolve($pv['visitable_type'])->find($pv['visitable_id']);
                $pvs = $pvs->push($objPv);

                $users->push($objPv->user);
                $detail = $objPv->details;
                if ($approvalSetting == 'Yes') {
                    ApprovalFlowUser::firstOrCreate([
                        'detail_id' => $detail->id,
                        'user_id' => $authUser->id,
                        'approval' => 1,
                    ]);
                    if ($pv['required'] == 1) {
                        $detail->user_id = $authUser->id;
                        $detail->approval = 1;
                        $detail->save();
                    }
                } else {
                    $detail->user_id = $authUser->id;
                    $detail->approval = 1;
                    $detail->save();
                }
            }
        });



        NotificationHelper::send(
            $users->unique('id'),
            new ChangePlanApprovedNotification('PV Get Approved', auth()->user())
        );
        // LogActivity::addLog();
        return response()->json(['status' => 'success']);
    }
    public function reject(Request $request)
    {
        $approvalSetting = ApprovalSetting::where('key', 'change_plan_approval_center_flow')->value('value');
        $users = collect([]);
        /**@var User $authUser */
        $authUser = Auth::user();
        foreach ($request->pvs as $pv) {
            $objPv = resolve($pv['visitable_type'])->find($pv['visitable_id']);
            $users->push($objPv->user);
            $detail = $objPv->details;
            if ($approvalSetting == 'Yes') {
                ApprovalFlowUser::firstOrCreate([
                    'detail_id' => $detail->id,
                    'user_id' => $authUser->id,
                    'approval' => 0,
                ]);
                if ($pv['required'] == 1) {
                    $detail->user_id = $authUser->id;
                    $detail->approval = 0;
                    $detail->save();
                }
            } else {
                $detail->user_id = $authUser->id;
                $detail->approval = 0;
                $detail->save();
            }
            if ($pv['reason_id'] != null) {
                $reason = Reason::find($pv['reason_id']);
                $objPv->reasons()->attach($reason, [
                    'user_id' => $authUser->id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
        }

        return response()->json(['status' => 'reject']);
    }
    public function filterOfEmployees(Request $request)
    {
        /**@var User $user */
        $user = Auth::user();
        $users = collect([]);
        $types = [DivisionType::class, Position::class];
        foreach ($request->line_id as $line) {
            foreach ($types as $type) {
                $users = $users->merge($user->planableUsers($line, ChangePlan::class, $type));
            }
        }
        return response()->json(['filtered_users' => $users->unique('id')->values()]);
    }
}

<?php


namespace App\Traits;

use App\Action;
use App\Exceptions\CrmException;
use App\Form;
use App\Helpers\LogActivity;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;
use Spatie\Permission\Models\Permission;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

trait ModelExportable
{
    /**
     * Export the model data to Excel format
     *
     * @param string $writerType The Excel writer type (e.g., 'xlsx', 'csv', 'xls')
     * @return BinaryFileResponse
     * @throws CrmException When export class doesn't exist or export fails
     */
    public static function export(string $writerType): BinaryFileResponse
    {
        try {
            $exportClassName = self::resolveExportClassName();
            $filename = self::generateExportFilename($writerType);

            // Log the export activity with proper context
            LogActivity::addLog(null, self::class, null, 'export');

            $response = Excel::download(new $exportClassName, $filename, $writerType);
            ob_end_clean();

            return $response;
        } catch (\Exception $e) {
            throw new CrmException("Export failed: " . $e->getMessage(), 1);
        }
    }

    /**
     * Resolve the export class name based on the model's namespace
     *
     * This method handles two namespace patterns:
     * - App\Models\* → App\Exports\*Export
     * - App\* → App\Exports\*Export
     *
     * @return string The fully qualified export class name
     * @throws CrmException When export class doesn't exist
     */
    private static function resolveExportClassName(): string
    {
        $modelClass = self::class;
        $pluralModelClass = Str::plural($modelClass);

        // Determine export class name based on namespace pattern
        if (Str::is("App\\Models\\*", $pluralModelClass)) {
            $exportClassName = Str::replaceFirst('App\Models', 'App\Exports', $pluralModelClass . 'Export');
        } else {
            $exportClassName = Str::replaceFirst('App', 'App\Exports', $pluralModelClass . 'Export');
        }

        // Validate that the export class exists
        if (!class_exists($exportClassName)) {
            throw new CrmException("Export class '{$exportClassName}' does not exist for model '" . class_basename($modelClass) . "'", 1);
        }

        return $exportClassName;
    }

    /**
     * Generate the export filename based on model name and writer type
     *
     * @param string $writerType The Excel writer type
     * @return string The generated filename
     */
    private static function generateExportFilename(string $writerType): string
    {
        $modelClass = self::class;
        $modelName = Str::lower(class_basename($modelClass));
        $writerTypeLower = Str::lower($writerType);

        return Str::plural($modelName) . '.' . $writerTypeLower;
    }
}
